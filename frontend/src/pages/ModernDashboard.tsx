import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Link } from 'react-router-dom';
import { 
  Users, 
  FileText, 
  TrendingUp, 
  Calendar,
  Building2,
  BarChart3,
  Plus,
  ArrowUpRight,
  Activity,
  Target,
  Clock,
  MapPin
} from 'lucide-react';

interface DashboardStats {
  totalCounts: number;
  todayCounts: number;
  instituteCounts: Array<{
    institute_id: string;
    institute_name: string;
    count: number;
  }>;
  productCounts: Array<{
    product_id: string;
    product_name: string;
    count: number;
  }>;
  instituteProductCounts: Array<{
    key: string;
    institute_id: string;
    institute_name: string;
    product_id: string;
    product_name: string;
    count: number;
    services: Array<{
      service_id: string;
      service_name: string;
      count: number;
      location_type: string;
      verifier_name: string;
    }>;
  }>;
  teamCounts: Array<{
    team_name: string;
    count: number;
  }>;
  locationCounts: {
    local: number;
    ogl: number;
    outstation: number;
  };
}

const ModernDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "http://localhost:3000/api/dashboard/stats",
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-slate-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      title: "Total Counts",
      value: stats?.totalCounts || 0,
      icon: BarChart3,
      description: "All time total",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      title: "Today's Counts",
      value: stats?.todayCounts || 0,
      icon: Calendar,
      description: "Today's entries",
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      title: "Institutes",
      value: stats?.instituteCounts?.length || 0,
      icon: Building2,
      description: "Assigned institutes",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      title: "Products",
      value: stats?.productCounts?.length || 0,
      icon: Target,
      description: "Assigned products",
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-slate-600 mt-2">
            Welcome back, <span className="font-semibold">{user?.name}</span>! Here's your overview.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg">
            <Link to="/daily-count">
              <Plus className="w-4 h-4 mr-2" />
              Add Daily Count
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="relative overflow-hidden border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-3xl font-bold text-slate-900">
                      {stat.value}
                    </p>
                    <p className="text-sm text-slate-500 mt-1 flex items-center">
                      <TrendingUp className="w-3 h-3 mr-1 text-green-500" />
                      {stat.description}
                    </p>
                  </div>
                  <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                    <Icon className={`w-6 h-6 ${stat.iconColor}`} />
                  </div>
                </div>
                <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${stat.color}`}></div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Institute & Product Counts */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2 text-blue-600" />
            Daily Counts by Institute & Product
          </CardTitle>
          <CardDescription>
            Detailed breakdown of your daily count entries
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stats?.instituteProductCounts && stats.instituteProductCounts.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {stats.instituteProductCounts.map((item) => (
                <Card key={item.key} className="border border-blue-100 bg-gradient-to-br from-blue-50/50 to-indigo-50/30">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-slate-900">{item.institute_name}</h4>
                        <p className="text-sm text-slate-600">{item.product_name}</p>
                      </div>
                      <div className="text-right">
                        <span className="text-2xl font-bold text-blue-600">{item.count}</span>
                        <p className="text-xs text-slate-500">total</p>
                      </div>
                    </div>
                    
                    {item.services && item.services.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">Services</p>
                        {item.services.map((service, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-white/60 rounded-lg">
                            <span className="text-sm font-medium">{service.service_name}</span>
                            <div className="flex items-center space-x-2">
                              <span className="font-semibold">{service.count}</span>
                              <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                                service.location_type === 'local' 
                                  ? 'bg-green-100 text-green-700'
                                  : service.location_type === 'ogl'
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {service.location_type}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-slate-900 mb-2">No daily counts recorded yet</h3>
              <p className="text-slate-600 mb-6">Start tracking your daily work by adding your first count entry.</p>
              <Button asChild className="bg-gradient-to-r from-blue-600 to-blue-700">
                <Link to="/daily-count">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Count
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Frequently used actions for faster workflow</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { icon: Plus, label: "Add Daily Count", href: "/daily-count", color: "from-blue-500 to-blue-600" },
              { icon: FileText, label: "View Reports", href: "/daily-count", color: "from-green-500 to-green-600" },
              { icon: Building2, label: "Manage Masters", href: "/masters", color: "from-purple-500 to-purple-600" },
              { icon: Users, label: "Team Overview", href: "/", color: "from-orange-500 to-orange-600" }
            ].map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={index}
                  asChild
                  variant="outline"
                  className="h-20 flex-col space-y-2 border-slate-200 hover:border-slate-300 hover:bg-slate-50"
                >
                  <Link to={action.href}>
                    <div className={`p-2 rounded-lg bg-gradient-to-r ${action.color}`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-sm font-medium">{action.label}</span>
                  </Link>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ModernDashboard;
