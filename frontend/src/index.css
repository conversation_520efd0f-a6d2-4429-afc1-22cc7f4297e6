/* Basic CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  background-color: #f8fafc;
  color: #0f172a;
  line-height: 1.5;
}

/* Utility Classes */
.min-h-screen {
  min-height: 100vh;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.flex-col {
  flex-direction: column;
}
.w-full {
  width: 100%;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Spacing */
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Typography */
.text-sm {
  font-size: 0.875rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.text-3xl {
  font-size: 1.875rem;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.text-center {
  text-align: center;
}

/* Colors */
.bg-white {
  background-color: white;
}
.bg-gray-50 {
  background-color: #f8fafc;
}
.bg-gray-900 {
  background-color: #0f172a;
}
.bg-blue-600 {
  background-color: #2563eb;
}
.bg-red-50 {
  background-color: #fef2f2;
}
.text-gray-600 {
  color: #475569;
}
.text-gray-700 {
  color: #334155;
}
.text-gray-900 {
  color: #0f172a;
}
.text-white {
  color: white;
}
.text-red-700 {
  color: #b91c1c;
}
.border-red-200 {
  border-color: #fecaca;
}

/* Borders and Radius */
.border {
  border: 1px solid #e2e8f0;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Inputs */
.input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Layout Classes */
.fixed {
  position: fixed;
}
.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.inset-y-0 {
  top: 0;
  bottom: 0;
}
.left-0 {
  left: 0;
}
.top-0 {
  top: 0;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.block {
  display: block;
}
.hidden {
  display: none;
}
.h-16 {
  height: 4rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-4 {
  height: 1rem;
}
.h-32 {
  height: 8rem;
}
.w-64 {
  width: 16rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-4 {
  width: 1rem;
}
.w-32 {
  width: 8rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-grow {
  flex-grow: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.gap-x-4 {
  column-gap: 1rem;
}
.gap-x-6 {
  column-gap: 1.5rem;
}
.gap-x-2 {
  column-gap: 0.5rem;
}
.space-y-1 > * + * {
  margin-top: 0.25rem;
}
.self-stretch {
  align-self: stretch;
}
.sticky {
  position: sticky;
}
.border-b {
  border-bottom-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-gray-200 {
  border-color: #e5e7eb;
}
.bg-gray-600 {
  background-color: #4b5563;
}
.bg-opacity-75 {
  background-color: rgb(75 85 99 / 0.75);
}
.bg-blue-100 {
  background-color: #dbeafe;
}
.bg-red-600 {
  background-color: #dc2626;
}
.bg-red-500 {
  background-color: #ef4444;
}
.text-blue-900 {
  color: #1e3a8a;
}
.text-gray-400 {
  color: #9ca3af;
}
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}
.hover\:text-gray-900:hover {
  color: #111827;
}
.hover\:bg-red-500:hover {
  background-color: #ef4444;
}
.mr-3 {
  margin-right: 0.75rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-auto {
  margin-right: auto;
}
.-m-2\.5 {
  margin: -0.625rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}
.group {
  /* group class for hover effects */
}

/* Responsive Classes */
.sm\:px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.sm\:gap-x-6 {
  column-gap: 1.5rem;
}
.lg\:hidden {
  display: none;
}
.lg\:flex {
  display: flex;
}
.lg\:fixed {
  position: fixed;
}
.lg\:inset-y-0 {
  top: 0;
  bottom: 0;
}
.lg\:w-64 {
  width: 16rem;
}
.lg\:flex-col {
  flex-direction: column;
}
.lg\:pl-64 {
  padding-left: 16rem;
}
.lg\:px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.lg\:gap-x-6 {
  column-gap: 1.5rem;
}

@media (min-width: 640px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:gap-x-6 {
    column-gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:hidden {
    display: none;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:fixed {
    position: fixed;
  }
  .lg\:inset-y-0 {
    top: 0;
    bottom: 0;
  }
  .lg\:w-64 {
    width: 16rem;
  }
  .lg\:flex-col {
    flex-direction: column;
  }
  .lg\:pl-64 {
    padding-left: 16rem;
  }
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .lg\:gap-x-6 {
    column-gap: 1.5rem;
  }
}

/* Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
