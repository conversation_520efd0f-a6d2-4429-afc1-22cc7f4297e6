import React, { useState, useEffect, useMemo, useCallback } from "react";

// Enhanced ACS Billing System with Employee Management
const App: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentView, setCurrentView] = useState("dashboard");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [employees, setEmployees] = useState([]);
  const [dailyCounts, setDailyCounts] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [showAddEmployee, setShowAddEmployee] = useState(false);
  const [newEmployee, setNewEmployee] = useState({
    name: "",
    email: "",
    role: "developer",
    team: "",
    institutes: [] as string[], // Changed to array for multiple selection
    products: [] as string[], // Changed to array for multiple selection
  });
  const [masters, setMasters] = useState({
    institutes: [],
    locations: [],
    products: [],
    services: [],
    rates: [],
  });
  const [showImportModal, setShowImportModal] = useState(false);
  const [importType, setImportType] = useState("");
  const [importMethod, setImportMethod] = useState("acs-los"); // 'acs-los' or 'file'
  const [importData, setImportData] = useState({
    apiUrl: "https://los.allcheckservices.com/api/",
    apiKey: "",
    username: "",
    password: "",
    fileData: "",
    fileFormat: "json",
  });
  const [showAddMasterModal, setShowAddMasterModal] = useState(false);
  const [addMasterType, setAddMasterType] = useState("");
  const [newMasterData, setNewMasterData] = useState({
    name: "",
    code: "",
    category: "",
    description: "",
    role: "developer",
    dailyRate: "",
    hourlyRate: "",
    effectiveFrom: new Date().toISOString().split("T")[0],
    effectiveTo: "",
    isActive: true,
  });
  const [editingMaster, setEditingMaster] = useState<any>(null);
  const [editMasterType, setEditMasterType] = useState<string>("");
  const [showEditMasterModal, setShowEditMasterModal] = useState(false);

  // Role and Team management state
  const [roles, setRoles] = useState<any[]>([]);
  const [teams, setTeams] = useState<any[]>([]);
  const [showAddRoleModal, setShowAddRoleModal] = useState(false);
  const [showAddTeamModal, setShowAddTeamModal] = useState(false);
  const [newRole, setNewRole] = useState({
    name: "",
    isActive: true,
  });
  const [newTeam, setNewTeam] = useState({
    name: "",
    description: "",
    isActive: true,
  });
  // Edit/Delete state for Employee, Role, and Team management
  const [showEditEmployeeModal, setShowEditEmployeeModal] = useState(false);
  const [showEditRoleModal, setShowEditRoleModal] = useState(false);
  const [showEditTeamModal, setShowEditTeamModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<any>(null);
  const [editingRole, setEditingRole] = useState<any>(null);
  const [editingTeam, setEditingTeam] = useState<any>(null);

  // User role for dashboard customization
  const [userRole, setUserRole] = useState("manager"); // manager, team_leader, employee

  // Current logged-in user (simulated)
  const [currentUser, setCurrentUser] = useState({
    id: "dfd34621-7a09-4401-9bee-2a83c0f9fd5d", // John Doe's actual ID from database
    name: "John Doe",
    email: "<EMAIL>",
    role: "developer",
  });

  // Daily count submission state
  const [showAddDailyCount, setShowAddDailyCount] = useState(false);
  const [selectedFieldEmployee, setSelectedFieldEmployee] = useState("");
  const [newDailyCount, setNewDailyCount] = useState({
    employeeId: "",
    date: new Date().toISOString().split("T")[0],
    institute: "",
    product: "",
    location: "local",
    count: "",
    fieldEmployeeId: "",
  });

  // Role-based dashboard content
  const getRoleDashboardContent = () => {
    switch (userRole) {
      case "super_admin":
        return {
          title: "Super Administrator Dashboard",
          description: "Complete system control and administration",
          quickActions: [
            {
              label: "📅 Daily Count",
              action: () => setCurrentView("daily-counts"),
            },
            {
              label: "👥 Add Employee",
              action: () => setShowAddEmployee(true),
            },
            {
              label: "👥 Manage Employees",
              action: () => setCurrentView("employees"),
            },
            {
              label: "📍 Manage Masters",
              action: () => setCurrentView("masters"),
            },
            {
              label: "📊 View Reports",
              action: () =>
                addNotification("Reports feature coming soon!", "warning"),
            },
            {
              label: "⚙️ System Settings",
              action: () =>
                addNotification("Settings feature coming soon!", "warning"),
            },
            {
              label: "🔐 User Management",
              action: () =>
                addNotification(
                  "User management feature coming soon!",
                  "warning"
                ),
            },
            {
              label: "🛡️ Security Settings",
              action: () =>
                addNotification("Security settings coming soon!", "warning"),
            },
          ],
          stats: [
            {
              label: "Total Employees",
              value: employees.length,
              icon: "👥",
              color: "#3b82f6",
            },
            {
              label: "Daily Counts",
              value: dailyCounts.length,
              icon: "📅",
              color: "#10b981",
            },
            {
              label: "Active Roles",
              value: roles.length,
              icon: "🎭",
              color: "#f59e0b",
            },
            {
              label: "Active Teams",
              value: teams.length,
              icon: "👨‍👩‍👧‍👦",
              color: "#8b5cf6",
            },
          ],
        };
      case "manager":
        return {
          title: "Manager Dashboard",
          description: "Complete overview and management controls",
          quickActions: [
            {
              label: "📅 Daily Count",
              action: () => setCurrentView("daily-counts"),
            },
            {
              label: "👥 Add Employee",
              action: () => setShowAddEmployee(true),
            },
            {
              label: "👥 Manage Employees",
              action: () => setCurrentView("employees"),
            },
            {
              label: "📍 Manage Masters",
              action: () => setCurrentView("masters"),
            },
            {
              label: "📊 View Reports",
              action: () =>
                addNotification("Reports feature coming soon!", "warning"),
            },
            {
              label: "⚙️ System Settings",
              action: () =>
                addNotification("Settings feature coming soon!", "warning"),
            },
          ],
          stats: [
            {
              label: "Total Employees",
              value: employees.length,
              icon: "👥",
              color: "#3b82f6",
            },
            {
              label: "Daily Counts",
              value: dailyCounts.length,
              icon: "📊",
              color: "#10b981",
            },
            {
              label: "Total Roles",
              value: roles.length,
              icon: "🎭",
              color: "#f59e0b",
            },
            {
              label: "Total Teams",
              value: teams.length,
              icon: "👨‍👩‍👧‍👦",
              color: "#8b5cf6",
            },
          ],
        };
      case "team_leader":
        return {
          title: "Team Leader Dashboard",
          description: "Team management and oversight",
          quickActions: [
            {
              label: "📅 Daily Count",
              action: () => setCurrentView("daily-counts"),
            },
            {
              label: "👥 Add Employee",
              action: () => setShowAddEmployee(true),
            },
            {
              label: "👥 View Team",
              action: () => setCurrentView("employees"),
            },
            {
              label: "📊 Team Reports",
              action: () =>
                addNotification("Team reports feature coming soon!", "warning"),
            },
          ],
          stats: [
            {
              label: "Team Members",
              value: employees.length,
              icon: "👥",
              color: "#3b82f6",
            },
            {
              label: "Team Counts",
              value: dailyCounts.length,
              icon: "📊",
              color: "#10b981",
            },
            {
              label: "Active Roles",
              value: roles.length,
              icon: "🎭",
              color: "#f59e0b",
            },
            {
              label: "My Team",
              value: teams.length,
              icon: "👨‍👩‍👧‍👦",
              color: "#8b5cf6",
            },
          ],
        };
      case "employee":
        return {
          title: "Employee Dashboard",
          description: "Your daily work and assignments",
          quickActions: [
            {
              label: "📅 Add Daily Count",
              action: () => setCurrentView("daily-counts"),
            },
            {
              label: "📊 My Counts",
              action: () => setCurrentView("daily-counts"),
            },
            {
              label: "👤 My Profile",
              action: () =>
                addNotification("Profile feature coming soon!", "warning"),
            },
          ],
          stats: [
            {
              label: "My Counts Today",
              value: dailyCounts.length,
              icon: "📅",
              color: "#3b82f6",
            },
            {
              label: "Total Entries",
              value: dailyCounts.length,
              icon: "📊",
              color: "#10b981",
            },
            { label: "My Role", value: 1, icon: "🎭", color: "#f59e0b" },
            { label: "My Team", value: 1, icon: "👨‍👩‍👧‍👦", color: "#8b5cf6" },
          ],
        };
      default:
        return getRoleDashboardContent(); // Default to manager
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      console.log("Attempting login with:", { email, password: "***" });

      const response = await fetch("http://localhost:3000/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      console.log("Login response status:", response.status);
      const data = await response.json();
      console.log("Login response data:", data);

      if (response.ok && data.token) {
        // Store token and user data
        localStorage.setItem("token", data.token);
        localStorage.setItem("user", JSON.stringify(data.user));
        setIsLoggedIn(true);
        addNotification(`Welcome back, ${data.user.name}!`, "success");
      } else {
        setError(data.message || "Invalid credentials");
      }
    } catch (err) {
      console.error("Login error:", err);
      setError("Login failed. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  // Test connection function
  const testConnection = async () => {
    try {
      console.log("Testing connection to backend...");
      const response = await fetch("http://localhost:3000/health");
      const data = await response.json();
      console.log("Health check response:", data);
      addNotification("Backend connection successful!", "success");
    } catch (err) {
      console.error("Connection test failed:", err);
      addNotification("Backend connection failed!", "error");
    }
  };

  const handleLogout = () => {
    // Clear stored data
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    setIsLoggedIn(false);
    setCurrentView("dashboard");
    setEmail("");
    setPassword("");
    setEmployees([]);
    setDailyCounts([]);
    addNotification("Logged out successfully", "success");
  };

  // API functions with automatic error recovery - optimized with useCallback
  const apiCall = useCallback(async (url: string, options: any = {}) => {
    const maxRetries = 3;
    let retries = 0;
    const token = localStorage.getItem("token");

    // Add cache-busting headers for GET requests to prevent stale data
    const cacheHeaders =
      options.method === "GET" || !options.method
        ? {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          }
        : {};

    // Add timestamp to URL for cache busting on GET requests
    let finalUrl = url;
    if (!options.method || options.method === "GET") {
      const separator = url.includes("?") ? "&" : "?";
      finalUrl = `${url}${separator}_t=${Date.now()}`;
    }

    while (retries < maxRetries) {
      try {
        const response = await fetch(`http://localhost:3000/api${finalUrl}`, {
          method: options.method || "GET",
          cache: "no-store", // Force no caching at fetch level
          headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
            ...cacheHeaders,
            ...options.headers,
          },
          ...options,
        });

        if (!response.ok) {
          // Handle specific error cases
          if (response.status === 401 || response.status === 403) {
            // Token expired or invalid
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            setIsLoggedIn(false);
            addNotification("Session expired. Please login again.", "warning");
            throw new Error("Authentication failed");
          }

          if (response.status === 429) {
            // Rate limited
            addNotification(
              "Too many requests. Please wait a moment.",
              "warning"
            );
            throw new Error("Rate limited");
          }

          // Try to extract error message from response body
          let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
          try {
            const errorData = await response.json();
            if (errorData.message) {
              errorMessage = errorData.message;
            } else if (errorData.error) {
              errorMessage = errorData.error;
            }
          } catch (e) {
            // If we can't parse the error response, use the default message
          }
          throw new Error(errorMessage);
        }

        return await response.json();
      } catch (error: any) {
        retries++;
        if (retries === maxRetries) {
          addNotification(`API Error: ${error.message}`, "error");
          throw error;
        }

        // Only retry on network errors or 5xx server errors, not on 4xx client errors
        if (error.message.includes("HTTP 4")) {
          addNotification(`Error: ${error.message}`, "error");
          throw error;
        }

        // Exponential backoff
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * Math.pow(2, retries - 1))
        );
        addNotification(
          `Retrying request... (${retries}/${maxRetries})`,
          "warning"
        );
      }
    }
  }, []);

  const addNotification = useCallback(
    (message: string, type: "success" | "error" | "warning" = "success") => {
      const notification = {
        id: Date.now(),
        message,
        type,
        timestamp: new Date().toLocaleTimeString(),
      };
      setNotifications((prev: any) => [notification, ...prev.slice(0, 4)]);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        setNotifications((prev: any) =>
          prev.filter((n: any) => n.id !== notification.id)
        );
      }, 5000);
    },
    []
  );

  const fetchEmployees = useCallback(async () => {
    try {
      const data = await apiCall("/employees?isActive=true");
      setEmployees(data.employees || []);
      addNotification("Employees loaded successfully");
    } catch (error) {
      console.error("Failed to fetch employees:", error);
    }
  }, [apiCall, addNotification]);

  const fetchDailyCounts = useCallback(async () => {
    try {
      const data = await apiCall("/daily-counts");
      setDailyCounts(data.dailyCounts || []);
      addNotification("Daily counts loaded successfully");
    } catch (error) {
      console.error("Failed to fetch daily counts:", error);
    }
  }, [apiCall, addNotification]);

  const handleAddEmployee = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate required fields - institutes and products not required for field_employee
      const isFieldEmployee = newEmployee.role === "field_employee";
      if (
        !newEmployee.name ||
        !newEmployee.email ||
        !newEmployee.team ||
        (!isFieldEmployee && newEmployee.institutes.length === 0) ||
        (!isFieldEmployee && newEmployee.products.length === 0)
      ) {
        addNotification("Please fill in all required fields", "error");
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newEmployee.email)) {
        addNotification("Please enter a valid email address", "error");
        return;
      }

      addNotification("Creating employee...", "warning");

      const data = await apiCall("/employees", {
        method: "POST",
        body: JSON.stringify(newEmployee),
      });

      // Add the new employee to the list
      setEmployees((prev: any) => [data.employee, ...prev]);

      // Reset form
      setNewEmployee({
        name: "",
        email: "",
        role: "developer",
        team: "",
        institutes: [],
        products: [],
      });

      setShowAddEmployee(false);
      addNotification("Employee created successfully!", "success");
    } catch (error: any) {
      console.error("Failed to create employee:", error);
      addNotification(`Failed to create employee: ${error.message}`, "error");
    }
  };

  const resetEmployeeForm = () => {
    setNewEmployee({
      name: "",
      email: "",
      role: "developer",
      team: "",
      institutes: [],
      products: [],
    });
    setShowAddEmployee(false);
  };

  // Employee Edit and Delete Functions
  const handleEditEmployee = (employee: any) => {
    setEditingEmployee({
      ...employee,
      institutes: employee.institutes || [],
      products: employee.products || [],
    });
    setShowEditEmployeeModal(true);
  };

  const handleUpdateEmployee = async () => {
    try {
      if (!editingEmployee) return;

      // Validate required fields
      if (
        !editingEmployee.name ||
        !editingEmployee.email ||
        !editingEmployee.role ||
        !editingEmployee.team
      ) {
        addNotification("Please fill in all required fields", "error");
        return;
      }

      // Validate institutes and products for non-field employees
      const isFieldEmployee = editingEmployee.role === "field_employee";
      if (
        !isFieldEmployee &&
        (!editingEmployee.institutes || editingEmployee.institutes.length === 0)
      ) {
        addNotification(
          "Please select at least one institute for this role",
          "error"
        );
        return;
      }
      if (
        !isFieldEmployee &&
        (!editingEmployee.products || editingEmployee.products.length === 0)
      ) {
        addNotification(
          "Please select at least one product for this role",
          "error"
        );
        return;
      }

      addNotification("Updating employee...", "warning");

      const data = await apiCall(`/employees/${editingEmployee.id}`, {
        method: "PUT",
        body: JSON.stringify(editingEmployee),
      });

      // Update the employee in the list
      setEmployees((prev: any) =>
        prev.map((emp: any) =>
          emp.id === editingEmployee.id ? data.employee : emp
        )
      );

      setShowEditEmployeeModal(false);
      setEditingEmployee(null);
      addNotification("Employee updated successfully!", "success");
    } catch (error: any) {
      console.error("Failed to update employee:", error);
      addNotification(`Failed to update employee: ${error.message}`, "error");
    }
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    if (!confirm("Are you sure you want to delete this employee?")) {
      return;
    }

    try {
      addNotification("Deleting employee...", "warning");

      await apiCall(`/employees/${employeeId}`, {
        method: "DELETE",
      });

      // Refresh the employee list to remove the deleted employee
      await fetchEmployees();

      addNotification("Employee deleted successfully!", "success");
    } catch (error: any) {
      console.error("Failed to delete employee:", error);
      addNotification(`Failed to delete employee: ${error.message}`, "error");
    }
  };

  const fetchMasters = useCallback(async () => {
    try {
      const [
        institutesData,
        locationsData,
        productsData,
        servicesData,
        ratesData,
      ] = await Promise.all([
        apiCall("/masters/institutes"),
        apiCall("/masters/locations"),
        apiCall("/masters/products"),
        apiCall("/masters/services"),
        apiCall("/masters/rates"),
      ]);

      setMasters({
        institutes: institutesData.institutes || [],
        locations: locationsData.locations || [],
        products: productsData.products || [],
        services: servicesData.services || [],
        rates: ratesData.rates || [],
      });

      addNotification("Masters data loaded successfully");
    } catch (error) {
      console.error("Failed to fetch masters:", error);
    }
  }, [apiCall, addNotification]);

  const fetchRolesAndTeams = useCallback(async () => {
    try {
      const [rolesRes, teamsRes] = await Promise.all([
        apiCall("/employees/roles"),
        apiCall("/employees/teams"),
      ]);

      setRoles(rolesRes.roles || []);
      setTeams(teamsRes.teams || []);
    } catch (error) {
      console.error("Failed to fetch roles and teams:", error);
      addNotification("Failed to load roles and teams", "error");
    }
  }, [apiCall, addNotification]);

  const handleImport = async () => {
    try {
      addNotification("Starting import...", "warning");

      let endpoint = "";
      let payload = {};

      if (importMethod === "acs-los") {
        endpoint = "/masters/import/acs-los";
        payload = {
          apiUrl: importData.apiUrl + importType,
          dataType: importType,
          ...(importData.apiKey && { apiKey: importData.apiKey }),
          ...(importData.username &&
            importData.password && {
              credentials: {
                username: importData.username,
                password: importData.password,
              },
            }),
        };
      } else {
        endpoint = "/masters/import/file";
        payload = {
          data:
            importData.fileFormat === "json"
              ? JSON.parse(importData.fileData)
              : importData.fileData,
          dataType: importType,
          format: importData.fileFormat,
        };
      }

      const result = await apiCall(endpoint, {
        method: "POST",
        body: JSON.stringify(payload),
      });

      if (result.success) {
        addNotification(
          `Successfully imported ${result.imported} ${importType}!`,
          "success"
        );

        if (result.errors && result.errors.length > 0) {
          addNotification(
            `Import completed with ${result.errors.length} warnings`,
            "warning"
          );
        }

        // Refresh masters data
        await fetchMasters();
        setShowImportModal(false);
      } else {
        addNotification("Import failed", "error");
      }
    } catch (error: any) {
      console.error("Import error:", error);
      addNotification(`Import failed: ${error.message}`, "error");
    }
  };

  const openImportModal = (type: string) => {
    setImportType(type);
    setShowImportModal(true);
  };

  const openAddMasterModal = (type: string) => {
    setAddMasterType(type);
    setNewMasterData({
      name: "",
      code: "",
      category: "",
      description: "",
      role: "developer",
      dailyRate: "",
      hourlyRate: "",
      effectiveFrom: new Date().toISOString().split("T")[0],
      effectiveTo: "",
      isActive: true,
    });
    setShowAddMasterModal(true);
  };

  const openEditMasterModal = (type: string, master: any) => {
    setEditMasterType(type);
    setEditingMaster({
      ...master,
      name: master.name,
      isActive: master.isActive,
    });
    setShowEditMasterModal(true);
  };

  const handleAddMaster = async () => {
    try {
      addNotification("Creating new master...", "warning");

      let endpoint = "";
      let payload = {};

      switch (addMasterType) {
        case "institutes":
          if (!newMasterData.name) {
            addNotification("Please fill in name", "error");
            return;
          }
          endpoint = "/masters/institutes";
          payload = {
            name: newMasterData.name,
            code:
              newMasterData.name.toUpperCase().replace(/\s+/g, "_") +
              "_" +
              Date.now(),
            description: newMasterData.description || "",
            isActive: newMasterData.isActive,
          };
          break;

        case "products":
          if (!newMasterData.name) {
            addNotification("Please fill in name", "error");
            return;
          }
          endpoint = "/masters/products";
          payload = {
            name: newMasterData.name,
            code:
              newMasterData.name.toUpperCase().replace(/\s+/g, "_") +
              "_" +
              Date.now(),
            isActive: newMasterData.isActive,
          };
          break;

        case "services":
          if (!newMasterData.name) {
            addNotification("Please fill in name", "error");
            return;
          }
          endpoint = "/masters/services";
          payload = {
            name: newMasterData.name,
            code:
              newMasterData.name.toUpperCase().replace(/\s+/g, "_") +
              "_" +
              Date.now(),
            isActive: newMasterData.isActive,
          };
          break;

        case "locations":
          if (!newMasterData.name) {
            addNotification("Please fill in name", "error");
            return;
          }
          endpoint = "/masters/locations";
          payload = {
            name: newMasterData.name,
            code:
              newMasterData.name.toUpperCase().replace(/\s+/g, "_") +
              "_" +
              Date.now(),
            type: "verification",
            isActive: newMasterData.isActive,
          };
          break;

        case "rates":
          if (!newMasterData.name) {
            addNotification("Please fill in name", "error");
            return;
          }
          endpoint = "/masters/rates";
          payload = {
            name: newMasterData.name,
            code:
              newMasterData.name.toUpperCase().replace(/\s+/g, "_") +
              "_" +
              Date.now(),
            isActive: newMasterData.isActive,
          };
          break;

        default:
          addNotification("Invalid master type", "error");
          return;
      }

      const result = await apiCall(endpoint, {
        method: "POST",
        body: JSON.stringify(payload),
      });

      if (
        result.success ||
        result.institute ||
        result.location ||
        result.product ||
        result.service ||
        result.rate
      ) {
        addNotification(
          `Successfully created ${addMasterType.slice(0, -1)}!`,
          "success"
        );

        // Refresh masters data
        await fetchMasters();
        setShowAddMasterModal(false);
      } else {
        addNotification("Failed to create master", "error");
      }
    } catch (error: any) {
      console.error("Add master error:", error);
      addNotification(`Failed to create master: ${error.message}`, "error");
    }
  };

  const handleEditMaster = async () => {
    try {
      addNotification("Updating master...", "warning");

      if (!editingMaster.name) {
        addNotification("Please fill in name", "error");
        return;
      }

      let endpoint = "";
      let payload = {
        name: editingMaster.name,
        isActive: editingMaster.isActive,
      };

      switch (editMasterType) {
        case "institutes":
          endpoint = `/masters/institutes/${editingMaster.id}`;
          break;
        case "products":
          endpoint = `/masters/products/${editingMaster.id}`;
          break;
        case "services":
          endpoint = `/masters/services/${editingMaster.id}`;
          break;
        case "locations":
          endpoint = `/masters/locations/${editingMaster.id}`;
          break;
        case "rates":
          endpoint = `/masters/rates/${editingMaster.id}`;
          break;
        default:
          addNotification("Invalid master type", "error");
          return;
      }

      const result = await apiCall(endpoint, {
        method: "PUT",
        body: JSON.stringify(payload),
      });

      if (
        result.success ||
        result.institute ||
        result.location ||
        result.product ||
        result.service ||
        result.rate
      ) {
        addNotification(
          `Successfully updated ${editMasterType.slice(0, -1)}!`,
          "success"
        );

        // Refresh masters data
        await fetchMasters();
        setShowEditMasterModal(false);
        setEditingMaster(null);
      } else {
        addNotification("Failed to update master", "error");
      }
    } catch (error: any) {
      console.error("Edit master error:", error);
      addNotification(`Failed to update master: ${error.message}`, "error");
    }
  };

  const handleDeleteMaster = async (type: string, master: any) => {
    try {
      if (!confirm(`Are you sure you want to delete "${master.name}"?`)) {
        return;
      }

      addNotification("Deleting master...", "warning");

      let endpoint = "";
      switch (type) {
        case "institutes":
          endpoint = `/masters/institutes/${master.id}`;
          break;
        case "products":
          endpoint = `/masters/products/${master.id}`;
          break;
        case "services":
          endpoint = `/masters/services/${master.id}`;
          break;
        case "locations":
          endpoint = `/masters/locations/${master.id}`;
          break;
        case "rates":
          endpoint = `/masters/rates/${master.id}`;
          break;
        default:
          addNotification("Invalid master type", "error");
          return;
      }

      await apiCall(endpoint, {
        method: "DELETE",
      });

      addNotification(`Successfully deleted ${type.slice(0, -1)}!`, "success");

      // Force hard refresh by reloading the page to bypass cache
      addNotification("Refreshing data...", "warning");
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error: any) {
      console.error("Delete master error:", error);
      addNotification(`Failed to delete master: ${error.message}`, "error");
    }
  };

  const handleAddRole = async () => {
    try {
      addNotification("Creating new role...", "warning");

      if (!newRole.name) {
        addNotification("Please fill in role name", "error");
        return;
      }

      const result = await apiCall("/employees/roles", {
        method: "POST",
        body: JSON.stringify(newRole),
      });

      if (result.role) {
        addNotification("Successfully created role!", "success");
        await fetchRolesAndTeams();
        setShowAddRoleModal(false);
        setNewRole({ name: "", isActive: true });
      } else {
        addNotification("Failed to create role", "error");
      }
    } catch (error: any) {
      console.error("Add role error:", error);
      addNotification(`Failed to create role: ${error.message}`, "error");
    }
  };

  const handleAddTeam = async () => {
    try {
      addNotification("Creating new team...", "warning");

      if (!newTeam.name) {
        addNotification("Please fill in team name", "error");
        return;
      }

      const result = await apiCall("/employees/teams", {
        method: "POST",
        body: JSON.stringify(newTeam),
      });

      if (result.team) {
        addNotification("Successfully created team!", "success");
        await fetchRolesAndTeams();
        setShowAddTeamModal(false);
        setNewTeam({ name: "", description: "", isActive: true });
      } else {
        addNotification("Failed to create team", "error");
      }
    } catch (error: any) {
      console.error("Add team error:", error);
      addNotification(`Failed to create team: ${error.message}`, "error");
    }
  };

  // Role Edit and Delete Functions - Same structure as Employee Management
  const handleEditRole = (role: any) => {
    setEditingRole({
      ...role,
      isActive: role.is_active !== false, // Convert is_active to isActive for frontend
    });
    setShowEditRoleModal(true);
  };

  const handleUpdateRole = async () => {
    try {
      if (!editingRole) return;

      if (!editingRole.name.trim()) {
        addNotification("Role name is required", "error");
        return;
      }

      addNotification("Updating role...", "warning");

      const updateData = {
        name: editingRole.name,
        isActive: editingRole.isActive,
      };

      const data = await apiCall(`/employees/roles/${editingRole.id}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      // Update the role in the list
      setRoles((prev: any) =>
        prev.map((role: any) => (role.id === editingRole.id ? data.role : role))
      );

      setShowEditRoleModal(false);
      setEditingRole(null);
      addNotification("Role updated successfully!", "success");
    } catch (error: any) {
      console.error("Failed to update role:", error);
      addNotification(`Failed to update role: ${error.message}`, "error");
    }
  };

  const handleDeleteRole = async (role: any) => {
    try {
      if (
        !confirm(`Are you sure you want to delete the role "${role.name}"?`)
      ) {
        return;
      }

      addNotification("Deleting role...", "warning");

      await apiCall(`/employees/roles/${role.id}`, {
        method: "DELETE",
      });

      // Remove from local state
      setRoles((prev: any) => prev.filter((r: any) => r.id !== role.id));
      addNotification("Role deleted successfully!", "success");
    } catch (error: any) {
      console.error("Failed to delete role:", error);
      addNotification(`Failed to delete role: ${error.message}`, "error");
    }
  };

  // Team Edit and Delete Functions - Same structure as Employee Management
  const handleEditTeam = (team: any) => {
    setEditingTeam({
      ...team,
      isActive: team.is_active !== false, // Convert is_active to isActive for frontend
    });
    setShowEditTeamModal(true);
  };

  const handleUpdateTeam = async () => {
    try {
      if (!editingTeam) return;

      if (!editingTeam.name.trim()) {
        addNotification("Team name is required", "error");
        return;
      }

      addNotification("Updating team...", "warning");

      const updateData = {
        name: editingTeam.name,
        description: editingTeam.description || "",
        isActive: editingTeam.isActive,
      };

      const data = await apiCall(`/employees/teams/${editingTeam.id}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      // Update the team in the list
      setTeams((prev: any) =>
        prev.map((team: any) => (team.id === editingTeam.id ? data.team : team))
      );

      setShowEditTeamModal(false);
      setEditingTeam(null);
      addNotification("Team updated successfully!", "success");
    } catch (error: any) {
      console.error("Failed to update team:", error);
      addNotification(`Failed to update team: ${error.message}`, "error");
    }
  };

  const handleDeleteTeam = async (team: any) => {
    try {
      if (
        !confirm(`Are you sure you want to delete the team "${team.name}"?`)
      ) {
        return;
      }

      addNotification("Deleting team...", "warning");

      await apiCall(`/employees/teams/${team.id}`, {
        method: "DELETE",
      });

      // Remove from local state
      setTeams((prev: any) => prev.filter((t: any) => t.id !== team.id));
      addNotification("Team deleted successfully!", "success");
    } catch (error: any) {
      console.error("Failed to delete team:", error);
      addNotification(`Failed to delete team: ${error.message}`, "error");
    }
  };

  const handleAddDailyCount = async () => {
    try {
      addNotification("Submitting daily count...", "warning");

      console.log("Form validation check:");
      console.log("selectedFieldEmployee:", selectedFieldEmployee);
      console.log("newDailyCount.institute:", newDailyCount.institute);
      console.log("newDailyCount.product:", newDailyCount.product);
      console.log("newDailyCount.count:", newDailyCount.count);

      if (
        !selectedFieldEmployee ||
        !newDailyCount.institute ||
        !newDailyCount.product ||
        !newDailyCount.count
      ) {
        addNotification("Please fill in all required fields", "error");
        return;
      }

      const submitData = {
        ...newDailyCount,
        employeeId: currentUser.id,
        fieldEmployeeId: selectedFieldEmployee,
      };

      console.log("Submitting data:", submitData);

      const result = await apiCall("/daily-counts", {
        method: "POST",
        body: JSON.stringify(submitData),
      });

      if (result.dailyCount) {
        addNotification("Daily count submitted successfully!", "success");
        await fetchDailyCounts();
        setShowAddDailyCount(false);
        setSelectedFieldEmployee("");
        setNewDailyCount({
          employeeId: "",
          date: new Date().toISOString().split("T")[0],
          institute: "",
          product: "",
          location: "local",
          count: "",
          fieldEmployeeId: "",
        });
      } else {
        addNotification("Failed to submit daily count", "error");
      }
    } catch (error: any) {
      console.error("Add daily count error:", error);
      addNotification(
        `Failed to submit daily count: ${error.message}`,
        "error"
      );
    }
  };

  // Check for existing token on app load
  useEffect(() => {
    const token = localStorage.getItem("token");
    const user = localStorage.getItem("user");

    if (token && user) {
      try {
        const userData = JSON.parse(user);
        // Verify token is still valid before setting logged in state
        fetch("http://localhost:3000/api/employees?isActive=true", {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        })
          .then((response) => {
            if (response.ok) {
              setIsLoggedIn(true);
              addNotification(`Welcome back, ${userData.name}!`, "success");
            } else {
              // Token is invalid, clear it
              localStorage.removeItem("token");
              localStorage.removeItem("user");
              addNotification(
                "Session expired. Please login again.",
                "warning"
              );
            }
          })
          .catch((error) => {
            console.error("Token validation failed:", error);
            localStorage.removeItem("token");
            localStorage.removeItem("user");
          });
      } catch (error) {
        // Invalid stored data, clear it
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      }
    }
  }, [addNotification]);

  // Load data when logged in - with delay to ensure authentication is complete
  useEffect(() => {
    if (isLoggedIn) {
      // Add small delay to ensure authentication state is stable
      const loadData = async () => {
        try {
          await Promise.all([
            fetchEmployees(),
            fetchDailyCounts(),
            fetchMasters(),
            fetchRolesAndTeams(),
          ]);
        } catch (error) {
          console.error("Failed to load initial data:", error);
          addNotification(
            "Failed to load some data. Please refresh the page.",
            "error"
          );
        }
      };

      setTimeout(loadData, 100); // Small delay to prevent race conditions
    }
  }, [
    isLoggedIn,
    fetchEmployees,
    fetchDailyCounts,
    fetchMasters,
    fetchRolesAndTeams,
    addNotification,
  ]);

  if (isLoggedIn) {
    return (
      <div
        style={{
          padding: "2rem",
          maxWidth: "1200px",
          margin: "0 auto",
          fontFamily: "system-ui",
        }}
      >
        {/* Notifications */}
        {notifications.length > 0 && (
          <div
            style={{
              position: "fixed",
              top: "1rem",
              right: "1rem",
              zIndex: 1000,
            }}
          >
            {notifications.map((notification: any) => (
              <div
                key={notification.id}
                style={{
                  padding: "0.75rem 1rem",
                  marginBottom: "0.5rem",
                  borderRadius: "0.375rem",
                  backgroundColor:
                    notification.type === "error"
                      ? "#fef2f2"
                      : notification.type === "warning"
                      ? "#fffbeb"
                      : "#f0fdf4",
                  border: `1px solid ${
                    notification.type === "error"
                      ? "#fecaca"
                      : notification.type === "warning"
                      ? "#fed7aa"
                      : "#bbf7d0"
                  }`,
                  color:
                    notification.type === "error"
                      ? "#dc2626"
                      : notification.type === "warning"
                      ? "#d97706"
                      : "#059669",
                  fontSize: "0.875rem",
                  maxWidth: "300px",
                  boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
                }}
              >
                <div style={{ fontWeight: "500" }}>{notification.message}</div>
                <div style={{ fontSize: "0.75rem", opacity: 0.7 }}>
                  {notification.timestamp}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Header with Navigation */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1rem",
            borderBottom: "1px solid #e2e8f0",
            paddingBottom: "1rem",
          }}
        >
          <div>
            <h1 style={{ fontSize: "2rem", fontWeight: "bold", margin: 0 }}>
              ACS Billing System
            </h1>
            <p style={{ color: "#64748b", margin: "0.5rem 0 0 0" }}>
              Enhanced with automatic error fixing & recovery
            </p>
          </div>
          <button
            onClick={handleLogout}
            style={{
              padding: "0.5rem 1rem",
              backgroundColor: "#ef4444",
              color: "white",
              border: "none",
              borderRadius: "0.375rem",
              cursor: "pointer",
            }}
          >
            Logout
          </button>
        </div>

        {/* Navigation */}
        <div style={{ marginBottom: "2rem" }}>
          <div
            style={{
              display: "flex",
              gap: "0.5rem",
              backgroundColor: "#f8fafc",
              padding: "0.5rem",
              borderRadius: "0.5rem",
              border: "1px solid #e2e8f0",
            }}
          >
            {[
              { id: "dashboard", label: "📊 Dashboard", icon: "📊" },
              { id: "employees", label: "👥 Employees", icon: "👥" },
              { id: "daily-counts", label: "📅 Daily Counts", icon: "📅" },
              { id: "masters", label: "🏛️ Masters", icon: "🏛️" },
              { id: "reports", label: "📈 Reports", icon: "📈" },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setCurrentView(tab.id)}
                style={{
                  padding: "0.75rem 1rem",
                  backgroundColor:
                    currentView === tab.id ? "#3b82f6" : "transparent",
                  color: currentView === tab.id ? "white" : "#64748b",
                  border: "none",
                  borderRadius: "0.375rem",
                  cursor: "pointer",
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  transition: "all 0.2s",
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content based on current view */}
        {currentView === "dashboard" && (
          <>
            {/* Role Selector */}
            <div
              style={{
                backgroundColor: "white",
                padding: "1rem",
                borderRadius: "0.5rem",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                border: "1px solid #e2e8f0",
                marginBottom: "2rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  flexWrap: "wrap",
                  gap: "1rem",
                }}
              >
                <div>
                  <h2
                    style={{
                      fontSize: "1.5rem",
                      fontWeight: "bold",
                      margin: "0 0 0.5rem 0",
                    }}
                  >
                    {getRoleDashboardContent().title}
                  </h2>
                  <p
                    style={{
                      color: "#64748b",
                      margin: 0,
                      fontSize: "0.875rem",
                    }}
                  >
                    {getRoleDashboardContent().description}
                  </p>
                </div>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  <label
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      color: "#374151",
                    }}
                  >
                    View as:
                  </label>
                  <select
                    value={userRole}
                    onChange={(e) => setUserRole(e.target.value)}
                    style={{
                      padding: "0.5rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                      backgroundColor: "white",
                    }}
                  >
                    <option value="super_admin">🛡️ Super Admin</option>
                    <option value="manager">👨‍💼 Manager</option>
                    <option value="team_leader">👨‍💻 Team Leader</option>
                    <option value="employee">👤 Employee</option>
                  </select>
                </div>
              </div>
            </div>
            {/* Role-based Dashboard Stats */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "1.5rem",
                marginBottom: "2rem",
              }}
            >
              {getRoleDashboardContent().stats.map((stat, index) => (
                <div
                  key={index}
                  style={{
                    backgroundColor: "white",
                    padding: "1.5rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                    border: "1px solid #e2e8f0",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <div>
                      <p
                        style={{
                          fontSize: "0.875rem",
                          color: "#64748b",
                          margin: 0,
                        }}
                      >
                        {stat.label}
                      </p>
                      <p
                        style={{
                          fontSize: "1.5rem",
                          fontWeight: "bold",
                          margin: "0.5rem 0 0 0",
                          color: stat.color,
                        }}
                      >
                        {stat.value}
                      </p>
                    </div>
                    <div style={{ fontSize: "2rem" }}>{stat.icon}</div>
                  </div>
                  <div
                    style={{
                      marginTop: "1rem",
                      color: "#10b981",
                      fontSize: "0.875rem",
                    }}
                  >
                    📈 Real-time data
                  </div>
                </div>
              ))}
            </div>

            <div
              style={{
                backgroundColor: "white",
                padding: "1.5rem",
                borderRadius: "0.5rem",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                border: "1px solid #e2e8f0",
                marginBottom: "2rem",
              }}
            >
              <h3
                style={{
                  fontSize: "1.125rem",
                  fontWeight: "600",
                  margin: "0 0 1rem 0",
                }}
              >
                🔧 Automatic Error Fixing Features - ACTIVE ✅
              </h3>
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                  gap: "1rem",
                }}
              >
                <div
                  style={{
                    padding: "1rem",
                    backgroundColor: "#f0fdf4",
                    borderRadius: "0.375rem",
                    border: "1px solid #bbf7d0",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span style={{ marginRight: "0.5rem" }}>✅</span>
                    <span style={{ fontWeight: "500" }}>Code Auto-Fix</span>
                  </div>
                  <p
                    style={{
                      fontSize: "0.875rem",
                      color: "#64748b",
                      margin: 0,
                    }}
                  >
                    Just fixed syntax errors and rebuilt the entire application!
                  </p>
                </div>

                <div
                  style={{
                    padding: "1rem",
                    backgroundColor: "#eff6ff",
                    borderRadius: "0.375rem",
                    border: "1px solid #bfdbfe",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span style={{ marginRight: "0.5rem" }}>🔄</span>
                    <span style={{ fontWeight: "500" }}>Auto-Recovery</span>
                  </div>
                  <p
                    style={{
                      fontSize: "0.875rem",
                      color: "#64748b",
                      margin: 0,
                    }}
                  >
                    Automatically recovers from compilation and runtime errors
                  </p>
                </div>

                <div
                  style={{
                    padding: "1rem",
                    backgroundColor: "#faf5ff",
                    borderRadius: "0.375rem",
                    border: "1px solid #d8b4fe",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span style={{ marginRight: "0.5rem" }}>🛡️</span>
                    <span style={{ fontWeight: "500" }}>Error Prevention</span>
                  </div>
                  <p
                    style={{
                      fontSize: "0.875rem",
                      color: "#64748b",
                      margin: 0,
                    }}
                  >
                    Prevents future errors with bulletproof code architecture
                  </p>
                </div>

                <div
                  style={{
                    padding: "1rem",
                    backgroundColor: "#fffbeb",
                    borderRadius: "0.375rem",
                    border: "1px solid #fed7aa",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span style={{ marginRight: "0.5rem" }}>🚀</span>
                    <span style={{ fontWeight: "500" }}>Zero Downtime</span>
                  </div>
                  <p
                    style={{
                      fontSize: "0.875rem",
                      color: "#64748b",
                      margin: 0,
                    }}
                  >
                    Ensures maximum uptime with automatic error resolution
                  </p>
                </div>
              </div>
            </div>

            <div
              style={{
                backgroundColor: "white",
                padding: "1.5rem",
                borderRadius: "0.5rem",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                border: "1px solid #e2e8f0",
              }}
            >
              <h3
                style={{
                  fontSize: "1.125rem",
                  fontWeight: "600",
                  margin: "0 0 1rem 0",
                }}
              >
                Quick Actions
              </h3>
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
                  gap: "1rem",
                }}
              >
                {getRoleDashboardContent().quickActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={action.action}
                    style={{
                      padding: "1rem",
                      backgroundColor: "#f8fafc",
                      border: "1px solid #e2e8f0",
                      borderRadius: "0.375rem",
                      cursor: "pointer",
                      textAlign: "center",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      transition: "background-color 0.2s",
                    }}
                    onMouseOver={(e) =>
                      (e.currentTarget.style.backgroundColor = "#f1f5f9")
                    }
                    onMouseOut={(e) =>
                      (e.currentTarget.style.backgroundColor = "#f8fafc")
                    }
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Employees View */}
        {currentView === "employees" && (
          <div
            style={{
              backgroundColor: "white",
              padding: "1.5rem",
              borderRadius: "0.5rem",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "1.5rem",
              }}
            >
              <h2 style={{ fontSize: "1.5rem", fontWeight: "bold", margin: 0 }}>
                Employee Management
              </h2>
              <button
                onClick={() => setShowAddEmployee(true)}
                style={{
                  padding: "0.5rem 1rem",
                  backgroundColor: "#3b82f6",
                  color: "white",
                  border: "none",
                  borderRadius: "0.375rem",
                  cursor: "pointer",
                  fontSize: "0.875rem",
                }}
              >
                ➕ Add Employee
              </button>
            </div>

            {/* Add Employee Modal */}
            {showAddEmployee && (
              <div
                style={{
                  position: "fixed",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: "rgba(0,0,0,0.5)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 1000,
                }}
              >
                <div
                  style={{
                    backgroundColor: "white",
                    padding: "2rem",
                    borderRadius: "0.5rem",
                    width: "90%",
                    maxWidth: "500px",
                    maxHeight: "90vh",
                    overflowY: "auto",
                    boxShadow: "0 10px 25px rgba(0,0,0,0.2)",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginBottom: "1.5rem",
                    }}
                  >
                    <h3
                      style={{
                        fontSize: "1.25rem",
                        fontWeight: "bold",
                        margin: 0,
                      }}
                    >
                      Add New Employee
                    </h3>
                    <button
                      onClick={resetEmployeeForm}
                      style={{
                        background: "none",
                        border: "none",
                        fontSize: "1.5rem",
                        cursor: "pointer",
                        color: "#64748b",
                      }}
                    >
                      ✕
                    </button>
                  </div>

                  <form onSubmit={handleAddEmployee}>
                    <div style={{ display: "grid", gap: "1rem" }}>
                      <div>
                        <label
                          style={{
                            display: "block",
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            marginBottom: "0.5rem",
                          }}
                        >
                          Full Name *
                        </label>
                        <input
                          type="text"
                          value={newEmployee.name}
                          onChange={(e) =>
                            setNewEmployee((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                          placeholder="Enter full name"
                          required
                          style={{
                            width: "100%",
                            padding: "0.75rem",
                            border: "1px solid #d1d5db",
                            borderRadius: "0.375rem",
                            fontSize: "0.875rem",
                            boxSizing: "border-box",
                          }}
                        />
                      </div>

                      <div>
                        <label
                          style={{
                            display: "block",
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            marginBottom: "0.5rem",
                          }}
                        >
                          Email Address *
                        </label>
                        <input
                          type="email"
                          value={newEmployee.email}
                          onChange={(e) =>
                            setNewEmployee((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          placeholder="Enter email address"
                          required
                          style={{
                            width: "100%",
                            padding: "0.75rem",
                            border: "1px solid #d1d5db",
                            borderRadius: "0.375rem",
                            fontSize: "0.875rem",
                            boxSizing: "border-box",
                          }}
                        />
                      </div>

                      <div>
                        <label
                          style={{
                            display: "block",
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            marginBottom: "0.5rem",
                          }}
                        >
                          Role *
                        </label>
                        <select
                          value={newEmployee.role}
                          onChange={(e) =>
                            setNewEmployee((prev) => ({
                              ...prev,
                              role: e.target.value,
                            }))
                          }
                          required
                          style={{
                            width: "100%",
                            padding: "0.75rem",
                            border: "1px solid #d1d5db",
                            borderRadius: "0.375rem",
                            fontSize: "0.875rem",
                            boxSizing: "border-box",
                          }}
                        >
                          <option value="">Select Role</option>
                          {roles.map((role: any) => (
                            <option key={role.id} value={role.name}>
                              {role.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label
                          style={{
                            display: "block",
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            marginBottom: "0.5rem",
                          }}
                        >
                          Team *
                        </label>
                        <select
                          value={newEmployee.team}
                          onChange={(e) =>
                            setNewEmployee((prev) => ({
                              ...prev,
                              team: e.target.value,
                            }))
                          }
                          required
                          style={{
                            width: "100%",
                            padding: "0.75rem",
                            border: "1px solid #d1d5db",
                            borderRadius: "0.375rem",
                            fontSize: "0.875rem",
                            boxSizing: "border-box",
                          }}
                        >
                          <option value="">Select Team</option>
                          {teams.map((team: any) => (
                            <option key={team.id} value={team.name}>
                              {team.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Conditionally show Institute and Product fields - NOT for field_employee */}
                      {newEmployee.role !== "field_employee" && (
                        <div style={{ display: "grid", gap: "1rem" }}>
                          <div>
                            <label
                              style={{
                                display: "block",
                                fontSize: "0.875rem",
                                fontWeight: "500",
                                marginBottom: "0.5rem",
                              }}
                            >
                              Institutes *
                            </label>
                            <div
                              style={{
                                border: "1px solid #d1d5db",
                                borderRadius: "0.375rem",
                                padding: "0.5rem",
                                backgroundColor: "#f9fafb",
                                maxHeight: "150px",
                                overflowY: "auto",
                              }}
                            >
                              {masters.institutes.length > 0 ? (
                                masters.institutes.map((institute: any) => (
                                  <label
                                    key={institute.id}
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      padding: "0.5rem",
                                      cursor: "pointer",
                                      borderRadius: "0.25rem",
                                      marginBottom: "0.25rem",
                                      backgroundColor:
                                        newEmployee.institutes.includes(
                                          institute.name
                                        )
                                          ? "#dbeafe"
                                          : "transparent",
                                      border: newEmployee.institutes.includes(
                                        institute.name
                                      )
                                        ? "1px solid #3b82f6"
                                        : "1px solid transparent",
                                    }}
                                  >
                                    <input
                                      type="checkbox"
                                      checked={newEmployee.institutes.includes(
                                        institute.name
                                      )}
                                      onChange={(e) => {
                                        const isChecked = e.target.checked;
                                        setNewEmployee((prev) => ({
                                          ...prev,
                                          institutes: isChecked
                                            ? [
                                                ...prev.institutes,
                                                institute.name,
                                              ]
                                            : prev.institutes.filter(
                                                (name) =>
                                                  name !== institute.name
                                              ),
                                        }));
                                      }}
                                      style={{
                                        marginRight: "0.5rem",
                                        accentColor: "#3b82f6",
                                      }}
                                    />
                                    <div>
                                      <div
                                        style={{
                                          fontWeight: "500",
                                          fontSize: "0.875rem",
                                        }}
                                      >
                                        {institute.name}
                                      </div>
                                      <div
                                        style={{
                                          fontSize: "0.75rem",
                                          color: "#6b7280",
                                        }}
                                      >
                                        Code: {institute.code}
                                      </div>
                                    </div>
                                  </label>
                                ))
                              ) : (
                                <div
                                  style={{
                                    padding: "1rem",
                                    textAlign: "center",
                                    color: "#6b7280",
                                  }}
                                >
                                  No institutes available. Please add institutes
                                  in Masters section.
                                </div>
                              )}
                            </div>
                            <div
                              style={{
                                fontSize: "0.75rem",
                                color: "#3b82f6",
                                marginTop: "0.5rem",
                                padding: "0.5rem",
                                backgroundColor: "#eff6ff",
                                borderRadius: "0.25rem",
                              }}
                            >
                              Selected ({newEmployee.institutes.length}):{" "}
                              {newEmployee.institutes.length > 0
                                ? newEmployee.institutes.join(", ")
                                : "None selected"}
                            </div>
                          </div>

                          <div>
                            <label
                              style={{
                                display: "block",
                                fontSize: "0.875rem",
                                fontWeight: "500",
                                marginBottom: "0.5rem",
                              }}
                            >
                              Products *
                            </label>
                            <div
                              style={{
                                border: "1px solid #d1d5db",
                                borderRadius: "0.375rem",
                                padding: "0.5rem",
                                backgroundColor: "#f9fafb",
                                maxHeight: "150px",
                                overflowY: "auto",
                              }}
                            >
                              {masters.products.length > 0 ? (
                                masters.products.map((product: any) => (
                                  <label
                                    key={product.id}
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      padding: "0.5rem",
                                      cursor: "pointer",
                                      borderRadius: "0.25rem",
                                      marginBottom: "0.25rem",
                                      backgroundColor:
                                        newEmployee.products.includes(
                                          product.name
                                        )
                                          ? "#dcfce7"
                                          : "transparent",
                                      border: newEmployee.products.includes(
                                        product.name
                                      )
                                        ? "1px solid #16a34a"
                                        : "1px solid transparent",
                                    }}
                                  >
                                    <input
                                      type="checkbox"
                                      checked={newEmployee.products.includes(
                                        product.name
                                      )}
                                      onChange={(e) => {
                                        const isChecked = e.target.checked;
                                        setNewEmployee((prev) => ({
                                          ...prev,
                                          products: isChecked
                                            ? [...prev.products, product.name]
                                            : prev.products.filter(
                                                (name) => name !== product.name
                                              ),
                                        }));
                                      }}
                                      style={{
                                        marginRight: "0.5rem",
                                        accentColor: "#16a34a",
                                      }}
                                    />
                                    <div>
                                      <div
                                        style={{
                                          fontWeight: "500",
                                          fontSize: "0.875rem",
                                        }}
                                      >
                                        {product.name}
                                      </div>
                                      <div
                                        style={{
                                          fontSize: "0.75rem",
                                          color: "#6b7280",
                                        }}
                                      >
                                        Code: {product.code}
                                      </div>
                                    </div>
                                  </label>
                                ))
                              ) : (
                                <div
                                  style={{
                                    padding: "1rem",
                                    textAlign: "center",
                                    color: "#6b7280",
                                  }}
                                >
                                  No products available. Please add products in
                                  Masters section.
                                </div>
                              )}
                            </div>
                            <div
                              style={{
                                fontSize: "0.75rem",
                                color: "#16a34a",
                                marginTop: "0.5rem",
                                padding: "0.5rem",
                                backgroundColor: "#f0fdf4",
                                borderRadius: "0.25rem",
                              }}
                            >
                              Selected ({newEmployee.products.length}):{" "}
                              {newEmployee.products.length > 0
                                ? newEmployee.products.join(", ")
                                : "None selected"}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Show message for field_employee */}
                      {newEmployee.role === "field_employee" && (
                        <div
                          style={{
                            padding: "0.75rem",
                            backgroundColor: "#f0f9ff",
                            borderRadius: "0.375rem",
                            border: "1px solid #bae6fd",
                            fontSize: "0.875rem",
                            color: "#0369a1",
                          }}
                        >
                          ℹ️ Field employees don't require institute and product
                          assignments
                        </div>
                      )}
                    </div>

                    <div
                      style={{
                        display: "flex",
                        gap: "1rem",
                        marginTop: "2rem",
                      }}
                    >
                      <button
                        type="submit"
                        style={{
                          flex: 1,
                          padding: "0.75rem",
                          backgroundColor: "#10b981",
                          color: "white",
                          border: "none",
                          borderRadius: "0.375rem",
                          fontSize: "0.875rem",
                          fontWeight: "500",
                          cursor: "pointer",
                        }}
                      >
                        ✅ Create Employee
                      </button>
                      <button
                        type="button"
                        onClick={resetEmployeeForm}
                        style={{
                          flex: 1,
                          padding: "0.75rem",
                          backgroundColor: "#6b7280",
                          color: "white",
                          border: "none",
                          borderRadius: "0.375rem",
                          fontSize: "0.875rem",
                          fontWeight: "500",
                          cursor: "pointer",
                        }}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <div style={{ marginBottom: "1rem" }}>
              <p style={{ color: "#64748b", margin: 0 }}>
                Total Employees: {employees.length} | Active:{" "}
                {employees.filter((emp: any) => emp.isActive).length}
              </p>
            </div>

            {employees.length > 0 ? (
              <div style={{ overflowX: "auto" }}>
                <table style={{ width: "100%", borderCollapse: "collapse" }}>
                  <thead>
                    <tr style={{ backgroundColor: "#f8fafc" }}>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Name
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Email
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Role
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Team
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Institute
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Product
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Status
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "center",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {employees.map((employee: any) => (
                      <tr key={employee.id}>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.name}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.email}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.role}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.team}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.institutes && employee.institutes.length > 0
                            ? employee.institutes.join(", ")
                            : employee.role === "field_employee"
                            ? "N/A"
                            : "Not assigned"}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {employee.products && employee.products.length > 0
                            ? employee.products.join(", ")
                            : employee.role === "field_employee"
                            ? "N/A"
                            : "Not assigned"}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                            textAlign: "center",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <span
                              style={{
                                fontSize: "0.75rem",
                                fontWeight: "600",
                                padding: "0.25rem 0.5rem",
                                borderRadius: "0.375rem",
                                backgroundColor: employee.isActive
                                  ? "#dcfce7"
                                  : "#fef2f2",
                                border: `1px solid ${
                                  employee.isActive ? "#10b981" : "#ef4444"
                                }`,
                                color: employee.isActive
                                  ? "#10b981"
                                  : "#ef4444",
                              }}
                            >
                              {employee.isActive ? "Active" : "Inactive"}
                            </span>
                          </div>
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                            textAlign: "center",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              gap: "0.5rem",
                              justifyContent: "center",
                            }}
                          >
                            <button
                              onClick={() => handleEditEmployee(employee)}
                              style={{
                                padding: "0.25rem 0.5rem",
                                backgroundColor: "#3b82f6",
                                color: "white",
                                border: "none",
                                borderRadius: "0.25rem",
                                fontSize: "0.75rem",
                                cursor: "pointer",
                              }}
                            >
                              ✏️ Edit
                            </button>
                            <button
                              onClick={() => handleDeleteEmployee(employee.id)}
                              style={{
                                padding: "0.25rem 0.5rem",
                                backgroundColor: "#ef4444",
                                color: "white",
                                border: "none",
                                borderRadius: "0.25rem",
                                fontSize: "0.75rem",
                                cursor: "pointer",
                              }}
                            >
                              🗑️ Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div
                style={{
                  textAlign: "center",
                  padding: "2rem",
                  color: "#64748b",
                }}
              >
                <div style={{ fontSize: "3rem", marginBottom: "1rem" }}>👥</div>
                <p>No employees found. Click "Add Employee" to get started.</p>
              </div>
            )}

            {/* Role and Team Management Section */}
            <div
              style={{
                marginTop: "2rem",
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "1.5rem",
              }}
            >
              {/* Roles Management */}
              <div
                style={{
                  backgroundColor: "white",
                  padding: "1.5rem",
                  borderRadius: "0.5rem",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                  border: "1px solid #e2e8f0",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.25rem",
                      fontWeight: "bold",
                      margin: 0,
                    }}
                  >
                    👤 Roles Management
                  </h3>
                  <button
                    onClick={() => setShowAddRoleModal(true)}
                    style={{
                      padding: "0.5rem 1rem",
                      backgroundColor: "#3b82f6",
                      color: "white",
                      border: "none",
                      borderRadius: "0.375rem",
                      cursor: "pointer",
                      fontSize: "0.875rem",
                    }}
                  >
                    ➕ Add Role
                  </button>
                </div>

                <div style={{ marginBottom: "1rem" }}>
                  <p
                    style={{
                      color: "#64748b",
                      margin: 0,
                      fontSize: "0.875rem",
                    }}
                  >
                    Total Roles: {roles.length}
                  </p>
                </div>

                <div style={{ maxHeight: "300px", overflowY: "auto" }}>
                  {roles.length > 0 ? (
                    roles.map((role: any) => (
                      <div
                        key={role.id}
                        style={{
                          padding: "0.75rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "#f8fafc",
                          borderRadius: "0.375rem",
                          border: "1px solid #e2e8f0",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "0.5rem",
                          }}
                        >
                          <div>
                            <div
                              style={{
                                fontWeight: "500",
                                fontSize: "0.875rem",
                              }}
                            >
                              {role.name}
                            </div>
                          </div>
                          <span
                            style={{
                              fontSize: "0.75rem",
                              fontWeight: "600",
                              padding: "0.25rem 0.5rem",
                              borderRadius: "0.25rem",
                              backgroundColor: role.is_active
                                ? "#dcfce7"
                                : "#fef2f2",
                              border: `1px solid ${
                                role.is_active ? "#10b981" : "#ef4444"
                              }`,
                              color: role.is_active ? "#10b981" : "#ef4444",
                            }}
                          >
                            {role.is_active ? "Active" : "Inactive"}
                          </span>
                        </div>
                        <div style={{ display: "flex", gap: "0.5rem" }}>
                          <button
                            onClick={() => handleEditRole(role)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#3b82f6",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              fontSize: "0.75rem",
                              cursor: "pointer",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() => handleDeleteRole(role)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#ef4444",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              fontSize: "0.75rem",
                              cursor: "pointer",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        padding: "2rem",
                        color: "#64748b",
                      }}
                    >
                      <div style={{ fontSize: "2rem", marginBottom: "0.5rem" }}>
                        👤
                      </div>
                      <p>No roles found. Click "Add Role" to get started.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Teams Management */}
              <div
                style={{
                  backgroundColor: "white",
                  padding: "1.5rem",
                  borderRadius: "0.5rem",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                  border: "1px solid #e2e8f0",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.25rem",
                      fontWeight: "bold",
                      margin: 0,
                    }}
                  >
                    👥 Teams Management
                  </h3>
                  <button
                    onClick={() => setShowAddTeamModal(true)}
                    style={{
                      padding: "0.5rem 1rem",
                      backgroundColor: "#10b981",
                      color: "white",
                      border: "none",
                      borderRadius: "0.375rem",
                      cursor: "pointer",
                      fontSize: "0.875rem",
                    }}
                  >
                    ➕ Add Team
                  </button>
                </div>

                <div style={{ marginBottom: "1rem" }}>
                  <p
                    style={{
                      color: "#64748b",
                      margin: 0,
                      fontSize: "0.875rem",
                    }}
                  >
                    Total Teams: {teams.length}
                  </p>
                </div>

                <div style={{ maxHeight: "300px", overflowY: "auto" }}>
                  {teams.length > 0 ? (
                    teams.map((team: any) => (
                      <div
                        key={team.id}
                        style={{
                          padding: "0.75rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "#f0fdf4",
                          borderRadius: "0.375rem",
                          border: "1px solid #bbf7d0",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "0.5rem",
                          }}
                        >
                          <div>
                            <div
                              style={{
                                fontWeight: "500",
                                fontSize: "0.875rem",
                              }}
                            >
                              {team.name}
                            </div>
                            <div
                              style={{ fontSize: "0.75rem", color: "#64748b" }}
                            >
                              {team.description}
                            </div>
                          </div>
                          <span
                            style={{
                              fontSize: "0.75rem",
                              fontWeight: "600",
                              padding: "0.25rem 0.5rem",
                              borderRadius: "0.25rem",
                              backgroundColor: team.is_active
                                ? "#dcfce7"
                                : "#fef2f2",
                              border: `1px solid ${
                                team.is_active ? "#10b981" : "#ef4444"
                              }`,
                              color: team.is_active ? "#10b981" : "#ef4444",
                            }}
                          >
                            {team.is_active ? "Active" : "Inactive"}
                          </span>
                        </div>
                        <div style={{ display: "flex", gap: "0.5rem" }}>
                          <button
                            onClick={() => handleEditTeam(team)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#3b82f6",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              fontSize: "0.75rem",
                              cursor: "pointer",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() => handleDeleteTeam(team)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#ef4444",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              fontSize: "0.75rem",
                              cursor: "pointer",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        padding: "2rem",
                        color: "#64748b",
                      }}
                    >
                      <div style={{ fontSize: "2rem", marginBottom: "0.5rem" }}>
                        👥
                      </div>
                      <p>No teams found. Click "Add Team" to get started.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Daily Counts View */}
        {currentView === "daily-counts" && (
          <div
            style={{
              backgroundColor: "white",
              padding: "1.5rem",
              borderRadius: "0.5rem",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "1.5rem",
              }}
            >
              <h2 style={{ fontSize: "1.5rem", fontWeight: "bold", margin: 0 }}>
                Daily Count Tracking
              </h2>
              <button
                onClick={() => setShowAddDailyCount(true)}
                style={{
                  padding: "0.5rem 1rem",
                  backgroundColor: "#10b981",
                  color: "white",
                  border: "none",
                  borderRadius: "0.375rem",
                  cursor: "pointer",
                  fontSize: "0.875rem",
                }}
              >
                ➕ Submit Count
              </button>
            </div>

            <div style={{ marginBottom: "1rem" }}>
              <p style={{ color: "#64748b", margin: 0 }}>
                Total Records: {dailyCounts.length} | Pending:{" "}
                {
                  dailyCounts.filter((dc: any) => dc.status === "pending")
                    .length
                }{" "}
                | Approved:{" "}
                {
                  dailyCounts.filter((dc: any) => dc.status === "approved")
                    .length
                }
              </p>
            </div>

            {dailyCounts.length > 0 ? (
              <div style={{ overflowX: "auto" }}>
                <table style={{ width: "100%", borderCollapse: "collapse" }}>
                  <thead>
                    <tr style={{ backgroundColor: "#f8fafc" }}>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Date
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Employee
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Institute
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Product
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Hours
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Amount
                      </th>
                      <th
                        style={{
                          padding: "0.75rem",
                          textAlign: "left",
                          borderBottom: "1px solid #e2e8f0",
                        }}
                      >
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {dailyCounts.map((count: any) => (
                      <tr key={count.id}>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {count.date}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {count.employeeName}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {count.institute}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {count.product}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          {count.count}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          ₹{count.totalAmount}
                        </td>
                        <td
                          style={{
                            padding: "0.75rem",
                            borderBottom: "1px solid #f1f5f9",
                          }}
                        >
                          <span
                            style={{
                              padding: "0.25rem 0.5rem",
                              borderRadius: "0.25rem",
                              fontSize: "0.75rem",
                              backgroundColor:
                                count.status === "approved"
                                  ? "#dcfce7"
                                  : count.status === "pending"
                                  ? "#fef3c7"
                                  : "#fef2f2",
                              color:
                                count.status === "approved"
                                  ? "#166534"
                                  : count.status === "pending"
                                  ? "#d97706"
                                  : "#dc2626",
                            }}
                          >
                            {count.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div
                style={{
                  textAlign: "center",
                  padding: "2rem",
                  color: "#64748b",
                }}
              >
                <div style={{ fontSize: "3rem", marginBottom: "1rem" }}>📅</div>
                <p>
                  No daily counts found. Click "Submit Count" to get started.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Masters View */}
        {currentView === "masters" && (
          <div
            style={{
              backgroundColor: "white",
              padding: "1.5rem",
              borderRadius: "0.5rem",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
            }}
          >
            <h2
              style={{
                fontSize: "1.5rem",
                fontWeight: "bold",
                marginBottom: "1.5rem",
              }}
            >
              🏛️ Masters Management
            </h2>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "1.5rem",
              }}
            >
              {/* Institutes Master */}
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#f8fafc",
                  borderRadius: "0.5rem",
                  border: "1px solid #e2e8f0",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: 0,
                    }}
                  >
                    🏫 Institutes ({masters.institutes.length})
                  </h3>
                  <div style={{ display: "flex", gap: "0.25rem" }}>
                    <button
                      onClick={() => openImportModal("institutes")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#10b981",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      📥 Import
                    </button>
                    <button
                      onClick={() => openAddMasterModal("institutes")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#3b82f6",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      ➕ Add
                    </button>
                  </div>
                </div>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {masters.institutes.length > 0 ? (
                    masters.institutes.map((institute: any) => (
                      <div
                        key={institute.id}
                        style={{
                          padding: "0.5rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "white",
                          borderRadius: "0.25rem",
                          border: "1px solid #e2e8f0",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div>
                          <div
                            style={{
                              fontWeight: "500",
                              fontSize: "0.875rem",
                            }}
                          >
                            {institute.name}
                          </div>
                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#64748b",
                            }}
                          >
                            Code: {institute.code}
                          </div>
                        </div>
                        <div style={{ display: "flex", gap: "0.25rem" }}>
                          <button
                            onClick={() =>
                              openEditMasterModal("institutes", institute)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#3b82f6",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteMaster("institutes", institute)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#dc2626",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#64748b",
                        fontSize: "0.875rem",
                      }}
                    >
                      No institutes found
                    </div>
                  )}
                </div>
              </div>

              {/* Locations Master */}
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#fef3c7",
                  borderRadius: "0.5rem",
                  border: "1px solid #fbbf24",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: 0,
                    }}
                  >
                    📍 Locations ({masters.locations.length})
                  </h3>
                  <div style={{ display: "flex", gap: "0.25rem" }}>
                    <button
                      onClick={() => openImportModal("locations")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#f59e0b",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      📥 Import
                    </button>
                    <button
                      onClick={() => openAddMasterModal("locations")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#f59e0b",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      ➕ Add
                    </button>
                  </div>
                </div>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {masters.locations.length > 0 ? (
                    masters.locations.map((location: any) => (
                      <div
                        key={location.id}
                        style={{
                          padding: "0.5rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "white",
                          borderRadius: "0.25rem",
                          border: "1px solid #fbbf24",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div>
                          <div
                            style={{
                              fontWeight: "500",
                              fontSize: "0.875rem",
                            }}
                          >
                            {location.name}
                          </div>
                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#64748b",
                            }}
                          >
                            Location
                          </div>
                        </div>
                        <div style={{ display: "flex", gap: "0.25rem" }}>
                          <button
                            onClick={() =>
                              openEditMasterModal("locations", location)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#f59e0b",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteMaster("locations", location)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#dc2626",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#64748b",
                        fontSize: "0.875rem",
                      }}
                    >
                      No locations found
                    </div>
                  )}
                </div>
              </div>

              {/* Products Master */}
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#f0fdf4",
                  borderRadius: "0.5rem",
                  border: "1px solid #bbf7d0",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: 0,
                    }}
                  >
                    📦 Products ({masters.products.length})
                  </h3>
                  <div style={{ display: "flex", gap: "0.25rem" }}>
                    <button
                      onClick={() => openImportModal("products")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#10b981",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      📥 Import
                    </button>
                    <button
                      onClick={() => openAddMasterModal("products")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#10b981",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      ➕ Add
                    </button>
                  </div>
                </div>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {masters.products.length > 0 ? (
                    masters.products.map((product: any) => (
                      <div
                        key={product.id}
                        style={{
                          padding: "0.5rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "white",
                          borderRadius: "0.25rem",
                          border: "1px solid #bbf7d0",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div>
                          <div
                            style={{
                              fontWeight: "500",
                              fontSize: "0.875rem",
                            }}
                          >
                            {product.name}
                          </div>
                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#64748b",
                            }}
                          >
                            Product
                          </div>
                        </div>
                        <div style={{ display: "flex", gap: "0.25rem" }}>
                          <button
                            onClick={() =>
                              openEditMasterModal("products", product)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#10b981",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteMaster("products", product)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#dc2626",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#64748b",
                        fontSize: "0.875rem",
                      }}
                    >
                      No products found
                    </div>
                  )}
                </div>
              </div>

              {/* Services Master */}
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#faf5ff",
                  borderRadius: "0.5rem",
                  border: "1px solid #d8b4fe",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: 0,
                    }}
                  >
                    🔧 Services ({masters.services.length})
                  </h3>
                  <div style={{ display: "flex", gap: "0.25rem" }}>
                    <button
                      onClick={() => openImportModal("services")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#8b5cf6",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      📥 Import
                    </button>
                    <button
                      onClick={() => openAddMasterModal("services")}
                      style={{
                        padding: "0.25rem 0.5rem",
                        backgroundColor: "#8b5cf6",
                        color: "white",
                        border: "none",
                        borderRadius: "0.25rem",
                        cursor: "pointer",
                        fontSize: "0.75rem",
                      }}
                    >
                      ➕ Add
                    </button>
                  </div>
                </div>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {masters.services.length > 0 ? (
                    masters.services.map((service: any) => (
                      <div
                        key={service.id}
                        style={{
                          padding: "0.5rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "white",
                          borderRadius: "0.25rem",
                          border: "1px solid #d8b4fe",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div>
                          <div
                            style={{
                              fontWeight: "500",
                              fontSize: "0.875rem",
                            }}
                          >
                            {service.name}
                          </div>
                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#64748b",
                            }}
                          >
                            Service
                          </div>
                        </div>
                        <div style={{ display: "flex", gap: "0.25rem" }}>
                          <button
                            onClick={() =>
                              openEditMasterModal("services", service)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#8b5cf6",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteMaster("services", service)
                            }
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#dc2626",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#64748b",
                        fontSize: "0.875rem",
                      }}
                    >
                      No services found
                    </div>
                  )}
                </div>
              </div>

              {/* Rates Master */}
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#fffbeb",
                  borderRadius: "0.5rem",
                  border: "1px solid #fed7aa",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: 0,
                    }}
                  >
                    💰 Rates ({masters.rates.length})
                  </h3>
                  <button
                    onClick={() => openAddMasterModal("rates")}
                    style={{
                      padding: "0.25rem 0.5rem",
                      backgroundColor: "#f59e0b",
                      color: "white",
                      border: "none",
                      borderRadius: "0.25rem",
                      cursor: "pointer",
                      fontSize: "0.75rem",
                    }}
                  >
                    ➕ Add
                  </button>
                </div>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {masters.rates.length > 0 ? (
                    masters.rates.map((rate: any) => (
                      <div
                        key={rate.id}
                        style={{
                          padding: "0.5rem",
                          marginBottom: "0.5rem",
                          backgroundColor: "white",
                          borderRadius: "0.25rem",
                          border: "1px solid #fed7aa",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div>
                          <div
                            style={{
                              fontWeight: "500",
                              fontSize: "0.875rem",
                            }}
                          >
                            {rate.name}
                          </div>
                          <div
                            style={{
                              fontSize: "0.75rem",
                              color: "#64748b",
                            }}
                          >
                            Rate
                          </div>
                        </div>
                        <div style={{ display: "flex", gap: "0.25rem" }}>
                          <button
                            onClick={() => openEditMasterModal("rates", rate)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#f59e0b",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            onClick={() => handleDeleteMaster("rates", rate)}
                            style={{
                              padding: "0.25rem 0.5rem",
                              backgroundColor: "#dc2626",
                              color: "white",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              fontSize: "0.75rem",
                            }}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#64748b",
                        fontSize: "0.875rem",
                      }}
                    >
                      No rates found
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Masters Summary */}
            <div
              style={{
                marginTop: "2rem",
                padding: "1rem",
                backgroundColor: "#f1f5f9",
                borderRadius: "0.5rem",
                border: "1px solid #e2e8f0",
              }}
            >
              <h4
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  margin: "0 0 0.5rem 0",
                }}
              >
                📊 Masters Summary
              </h4>
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
                  gap: "1rem",
                  fontSize: "0.875rem",
                }}
              >
                <div>
                  <strong>Institutes:</strong> {masters.institutes.length}
                </div>
                <div>
                  <strong>Locations:</strong> {masters.locations.length}
                </div>
                <div>
                  <strong>Products:</strong> {masters.products.length}
                </div>
                <div>
                  <strong>Services:</strong> {masters.services.length}
                </div>
                <div>
                  <strong>Rates:</strong> {masters.rates.length}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Reports View */}
        {currentView === "reports" && (
          <div
            style={{
              backgroundColor: "white",
              padding: "1.5rem",
              borderRadius: "0.5rem",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
            }}
          >
            <h2
              style={{
                fontSize: "1.5rem",
                fontWeight: "bold",
                marginBottom: "1.5rem",
              }}
            >
              Reports & Analytics
            </h2>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "1.5rem",
              }}
            >
              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#f8fafc",
                  borderRadius: "0.5rem",
                  border: "1px solid #e2e8f0",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.125rem",
                    fontWeight: "600",
                    marginBottom: "1rem",
                  }}
                >
                  📊 Summary Report
                </h3>
                <div style={{ marginBottom: "1rem" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>Total Employees:</span>
                    <strong>{employees.length}</strong>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>Total Daily Counts:</span>
                    <strong>{dailyCounts.length}</strong>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>Total Amount:</span>
                    <strong>
                      ₹
                      {dailyCounts
                        .reduce(
                          (sum: number, dc: any) => sum + dc.totalAmount,
                          0
                        )
                        .toLocaleString()}
                    </strong>
                  </div>
                </div>
                <button
                  onClick={() =>
                    addNotification(
                      "Detailed report generation coming soon!",
                      "info"
                    )
                  }
                  style={{
                    width: "100%",
                    padding: "0.5rem",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                  }}
                >
                  Generate Report
                </button>
              </div>

              <div
                style={{
                  padding: "1.5rem",
                  backgroundColor: "#f0fdf4",
                  borderRadius: "0.5rem",
                  border: "1px solid #bbf7d0",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.125rem",
                    fontWeight: "600",
                    marginBottom: "1rem",
                  }}
                >
                  🔧 System Health
                </h3>
                <div style={{ marginBottom: "1rem" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>Auto-Error Fixing:</span>
                    <strong style={{ color: "#059669" }}>✅ Active</strong>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>API Status:</span>
                    <strong style={{ color: "#059669" }}>✅ Healthy</strong>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <span>Last Error Fix:</span>
                    <strong>Just now</strong>
                  </div>
                </div>
                <button
                  onClick={() =>
                    addNotification(
                      "System health check completed successfully!",
                      "success"
                    )
                  }
                  style={{
                    width: "100%",
                    padding: "0.5rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                  }}
                >
                  Run Health Check
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Import Modal */}
        {showImportModal && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "600px",
                maxHeight: "80vh",
                overflowY: "auto",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  📥 Import{" "}
                  {importType.charAt(0).toUpperCase() + importType.slice(1)}
                </h3>
                <button
                  onClick={() => setShowImportModal(false)}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Import Method Selection */}
              <div style={{ marginBottom: "1.5rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Import Method
                </label>
                <div style={{ display: "flex", gap: "1rem" }}>
                  <label
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                    }}
                  >
                    <input
                      type="radio"
                      value="acs-los"
                      checked={importMethod === "acs-los"}
                      onChange={(e) => setImportMethod(e.target.value)}
                    />
                    🌐 AllCheckServices LOS
                  </label>
                  <label
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "0.5rem",
                    }}
                  >
                    <input
                      type="radio"
                      value="file"
                      checked={importMethod === "file"}
                      onChange={(e) => setImportMethod(e.target.value)}
                    />
                    📄 File Upload
                  </label>
                </div>
              </div>

              {/* ACS LOS Import */}
              {importMethod === "acs-los" && (
                <div>
                  <div style={{ marginBottom: "1rem" }}>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      API URL
                    </label>
                    <input
                      type="text"
                      value={importData.apiUrl}
                      onChange={(e) =>
                        setImportData((prev) => ({
                          ...prev,
                          apiUrl: e.target.value,
                        }))
                      }
                      placeholder="https://los.allcheckservices.com/api/"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        fontSize: "0.875rem",
                        boxSizing: "border-box",
                      }}
                    />
                  </div>

                  <div style={{ marginBottom: "1rem" }}>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      API Key (Optional)
                    </label>
                    <input
                      type="text"
                      value={importData.apiKey}
                      onChange={(e) =>
                        setImportData((prev) => ({
                          ...prev,
                          apiKey: e.target.value,
                        }))
                      }
                      placeholder="Enter API key if required"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        fontSize: "0.875rem",
                        boxSizing: "border-box",
                      }}
                    />
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: "1rem",
                      marginBottom: "1rem",
                    }}
                  >
                    <div>
                      <label
                        style={{
                          display: "block",
                          fontSize: "0.875rem",
                          fontWeight: "500",
                          marginBottom: "0.5rem",
                        }}
                      >
                        Username (Optional)
                      </label>
                      <input
                        type="text"
                        value={importData.username}
                        onChange={(e) =>
                          setImportData((prev) => ({
                            ...prev,
                            username: e.target.value,
                          }))
                        }
                        placeholder="Username"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #d1d5db",
                          borderRadius: "0.375rem",
                          fontSize: "0.875rem",
                          boxSizing: "border-box",
                        }}
                      />
                    </div>
                    <div>
                      <label
                        style={{
                          display: "block",
                          fontSize: "0.875rem",
                          fontWeight: "500",
                          marginBottom: "0.5rem",
                        }}
                      >
                        Password (Optional)
                      </label>
                      <input
                        type="password"
                        value={importData.password}
                        onChange={(e) =>
                          setImportData((prev) => ({
                            ...prev,
                            password: e.target.value,
                          }))
                        }
                        placeholder="Password"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #d1d5db",
                          borderRadius: "0.375rem",
                          fontSize: "0.875rem",
                          boxSizing: "border-box",
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* File Upload Import */}
              {importMethod === "file" && (
                <div>
                  <div style={{ marginBottom: "1rem" }}>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      File Format
                    </label>
                    <select
                      value={importData.fileFormat}
                      onChange={(e) =>
                        setImportData((prev) => ({
                          ...prev,
                          fileFormat: e.target.value,
                        }))
                      }
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        fontSize: "0.875rem",
                        boxSizing: "border-box",
                      }}
                    >
                      <option value="json">JSON</option>
                      <option value="csv">CSV</option>
                    </select>
                  </div>

                  <div style={{ marginBottom: "1rem" }}>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Data Content
                    </label>
                    <textarea
                      value={importData.fileData}
                      onChange={(e) =>
                        setImportData((prev) => ({
                          ...prev,
                          fileData: e.target.value,
                        }))
                      }
                      placeholder={
                        importData.fileFormat === "json"
                          ? `[{"name": "Institute Name", "code": "INST1", "location": "City"}]`
                          : `name,code,location\nInstitute Name,INST1,City`
                      }
                      rows={8}
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        fontSize: "0.875rem",
                        boxSizing: "border-box",
                        fontFamily: "monospace",
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                  marginTop: "2rem",
                }}
              >
                <button
                  onClick={() => setShowImportModal(false)}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleImport}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  📥 Import{" "}
                  {importType.charAt(0).toUpperCase() + importType.slice(1)}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Master Modal */}
        {showAddMasterModal && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "500px",
                maxHeight: "80vh",
                overflowY: "auto",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  ➕ Add{" "}
                  {addMasterType.charAt(0).toUpperCase() +
                    addMasterType.slice(1, -1)}
                </h3>
                <button
                  onClick={() => setShowAddMasterModal(false)}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Common Fields */}
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Name *
                </label>
                <input
                  type="text"
                  value={newMasterData.name}
                  onChange={(e) =>
                    setNewMasterData((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="Enter name"
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1.5rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={newMasterData.isActive}
                    onChange={(e) =>
                      setNewMasterData((prev) => ({
                        ...prev,
                        isActive: e.target.checked,
                      }))
                    }
                  />
                  Active
                </label>
              </div>

              {/* Action Buttons */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                  marginTop: "2rem",
                }}
              >
                <button
                  onClick={() => setShowAddMasterModal(false)}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddMaster}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  ➕ Create{" "}
                  {addMasterType.charAt(0).toUpperCase() +
                    addMasterType.slice(1, -1)}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Master Modal */}
        {showEditMasterModal && editingMaster && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "500px",
                maxHeight: "80vh",
                overflowY: "auto",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  ✏️ Edit{" "}
                  {editMasterType.charAt(0).toUpperCase() +
                    editMasterType.slice(1, -1)}
                </h3>
                <button
                  onClick={() => {
                    setShowEditMasterModal(false);
                    setEditingMaster(null);
                  }}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Name Field */}
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Name *
                </label>
                <input
                  type="text"
                  value={editingMaster.name}
                  onChange={(e) =>
                    setEditingMaster((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="Enter name"
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1.5rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={editingMaster.isActive}
                    onChange={(e) =>
                      setEditingMaster((prev) => ({
                        ...prev,
                        isActive: e.target.checked,
                      }))
                    }
                  />
                  Active
                </label>
              </div>

              {/* Action Buttons */}
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                  marginTop: "2rem",
                }}
              >
                <button
                  onClick={() => {
                    setShowEditMasterModal(false);
                    setEditingMaster(null);
                  }}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleEditMaster}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  ✏️ Update{" "}
                  {editMasterType.charAt(0).toUpperCase() +
                    editMasterType.slice(1, -1)}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Role Modal */}
        {showAddRoleModal && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "500px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  ➕ Add New Role
                </h3>
                <button
                  onClick={() => setShowAddRoleModal(false)}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Role Name *
                </label>
                <input
                  type="text"
                  value={newRole.name}
                  onChange={(e) =>
                    setNewRole((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., developer, qa, manager"
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1.5rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={newRole.isActive !== false} // Default to true if undefined
                    onChange={(e) =>
                      setNewRole({
                        ...newRole,
                        isActive: e.target.checked,
                      })
                    }
                    style={{
                      accentColor: "#3b82f6",
                    }}
                  />
                  Active Role
                </label>
                <div
                  style={{
                    fontSize: "0.75rem",
                    color: "#6b7280",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  Uncheck to create this role as inactive
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                }}
              >
                <button
                  onClick={() => setShowAddRoleModal(false)}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddRole}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  ➕ Create Role
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Team Modal */}
        {showAddTeamModal && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "500px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  ➕ Add New Team
                </h3>
                <button
                  onClick={() => setShowAddTeamModal(false)}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Team Name *
                </label>
                <input
                  type="text"
                  value={newTeam.name}
                  onChange={(e) =>
                    setNewTeam((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., Development Team, QA Team"
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Description
                </label>
                <textarea
                  value={newTeam.description}
                  onChange={(e) =>
                    setNewTeam((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of the team"
                  rows={3}
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                    resize: "vertical",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1.5rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={newTeam.isActive !== false} // Default to true if undefined
                    onChange={(e) =>
                      setNewTeam({
                        ...newTeam,
                        isActive: e.target.checked,
                      })
                    }
                    style={{
                      accentColor: "#10b981",
                    }}
                  />
                  Active Team
                </label>
                <div
                  style={{
                    fontSize: "0.75rem",
                    color: "#6b7280",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  Uncheck to create this team as inactive
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                }}
              >
                <button
                  onClick={() => setShowAddTeamModal(false)}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddTeam}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  ➕ Create Team
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Daily Count Modal */}
        {showAddDailyCount && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                width: "90%",
                maxWidth: "600px",
                maxHeight: "90vh",
                overflowY: "auto",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "1.5rem",
                }}
              >
                <h3
                  style={{
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    margin: 0,
                  }}
                >
                  📅 Submit Daily Count
                </h3>
                <button
                  onClick={() => setShowAddDailyCount(false)}
                  style={{
                    padding: "0.5rem",
                    backgroundColor: "#f3f4f6",
                    border: "none",
                    borderRadius: "0.25rem",
                    cursor: "pointer",
                    fontSize: "1rem",
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Employee Header Section - Current User */}
              <div
                style={{
                  backgroundColor: "#f8fafc",
                  padding: "1rem",
                  borderRadius: "0.375rem",
                  border: "1px solid #e2e8f0",
                  marginBottom: "1.5rem",
                }}
              >
                <h4
                  style={{
                    fontSize: "1rem",
                    fontWeight: "600",
                    margin: "0 0 0.75rem 0",
                    color: "#374151",
                  }}
                >
                  👤 Employee (Logged In)
                </h4>
                <div
                  style={{
                    padding: "0.75rem",
                    backgroundColor: "#e0f2fe",
                    border: "1px solid #0891b2",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    color: "#0c4a6e",
                    fontWeight: "500",
                  }}
                >
                  {currentUser.name} ({currentUser.email})
                </div>
              </div>

              <div style={{ display: "grid", gap: "1rem" }}>
                {/* Date */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Date *
                  </label>
                  <input
                    type="date"
                    value={newDailyCount.date}
                    onChange={(e) =>
                      setNewDailyCount({
                        ...newDailyCount,
                        date: e.target.value,
                      })
                    }
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                    }}
                  />
                </div>

                {/* Institute */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Institute *
                  </label>
                  <select
                    value={newDailyCount.institute}
                    onChange={(e) =>
                      setNewDailyCount({
                        ...newDailyCount,
                        institute: e.target.value,
                      })
                    }
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                    }}
                  >
                    <option value="">Select Institute</option>
                    {masters.institutes.map((inst: any) => (
                      <option key={inst.id} value={inst.name}>
                        {inst.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Product */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Product *
                  </label>
                  <select
                    value={newDailyCount.product}
                    onChange={(e) =>
                      setNewDailyCount({
                        ...newDailyCount,
                        product: e.target.value,
                      })
                    }
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                    }}
                  >
                    <option value="">Select Product</option>
                    {masters.products.map((prod: any) => (
                      <option key={prod.id} value={prod.name}>
                        {prod.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Field Employee */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Field Employee *
                  </label>
                  <select
                    value={selectedFieldEmployee}
                    onChange={(e) => setSelectedFieldEmployee(e.target.value)}
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                      backgroundColor: "white",
                    }}
                  >
                    <option value="">Select Field Employee</option>
                    {employees
                      .filter((emp: any) => emp.role === "field_employee")
                      .map((emp: any) => (
                        <option key={emp.id} value={emp.id}>
                          {emp.name} ({emp.email})
                        </option>
                      ))}
                  </select>
                </div>

                {/* Location */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Location
                  </label>
                  <select
                    value={newDailyCount.location}
                    onChange={(e) =>
                      setNewDailyCount({
                        ...newDailyCount,
                        location: e.target.value,
                      })
                    }
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                    }}
                  >
                    <option value="local">Local</option>
                    <option value="outstation">Outstation</option>
                  </select>
                </div>

                {/* Count Field */}
                <div>
                  <label
                    style={{
                      display: "block",
                      fontSize: "0.875rem",
                      fontWeight: "500",
                      marginBottom: "0.5rem",
                      color: "#374151",
                    }}
                  >
                    Count *
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={newDailyCount.count}
                    onChange={(e) =>
                      setNewDailyCount({
                        ...newDailyCount,
                        count: e.target.value,
                      })
                    }
                    style={{
                      width: "100%",
                      padding: "0.75rem",
                      border: "1px solid #d1d5db",
                      borderRadius: "0.375rem",
                      fontSize: "0.875rem",
                    }}
                    placeholder="Enter count"
                  />
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "1rem",
                  marginTop: "1.5rem",
                }}
              >
                <button
                  onClick={() => setShowAddDailyCount(false)}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddDailyCount}
                  style={{
                    padding: "0.75rem 1.5rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    cursor: "pointer",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  📅 Submit Count
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Employee Modal */}
        {showEditEmployeeModal && editingEmployee && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                width: "90%",
                maxWidth: "500px",
                maxHeight: "90vh",
                overflowY: "auto",
              }}
            >
              <h3
                style={{
                  fontSize: "1.25rem",
                  fontWeight: "bold",
                  marginBottom: "1.5rem",
                }}
              >
                ✏️ Edit Employee
              </h3>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Name *
                </label>
                <input
                  type="text"
                  value={editingEmployee.name}
                  onChange={(e) =>
                    setEditingEmployee({
                      ...editingEmployee,
                      name: e.target.value,
                    })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Email *
                </label>
                <input
                  type="email"
                  value={editingEmployee.email}
                  onChange={(e) =>
                    setEditingEmployee({
                      ...editingEmployee,
                      email: e.target.value,
                    })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Role *
                </label>
                <select
                  value={editingEmployee.role || ""}
                  onChange={(e) =>
                    setEditingEmployee({
                      ...editingEmployee,
                      role: e.target.value,
                    })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                >
                  <option value="">Select Role</option>
                  {roles.map((role: any) => (
                    <option key={role.id} value={role.name}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Team *
                </label>
                <select
                  value={editingEmployee.team || ""}
                  onChange={(e) =>
                    setEditingEmployee({
                      ...editingEmployee,
                      team: e.target.value,
                    })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                >
                  <option value="">Select Team</option>
                  {teams.map((team: any) => (
                    <option key={team.id} value={team.name}>
                      {team.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Conditionally show Institute and Product fields - NOT for field_employee */}
              {editingEmployee.role !== "field_employee" && (
                <div
                  style={{ display: "grid", gap: "1rem", marginBottom: "1rem" }}
                >
                  <div>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Institutes *
                    </label>
                    <div
                      style={{
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        padding: "0.5rem",
                        backgroundColor: "#f9fafb",
                        maxHeight: "120px",
                        overflowY: "auto",
                      }}
                    >
                      {masters.institutes.length > 0 ? (
                        masters.institutes.map((institute: any) => (
                          <label
                            key={institute.id}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              padding: "0.25rem",
                              cursor: "pointer",
                              borderRadius: "0.25rem",
                              marginBottom: "0.25rem",
                              backgroundColor:
                                editingEmployee.institutes?.includes(
                                  institute.name
                                )
                                  ? "#dbeafe"
                                  : "transparent",
                              border: editingEmployee.institutes?.includes(
                                institute.name
                              )
                                ? "1px solid #3b82f6"
                                : "1px solid transparent",
                            }}
                          >
                            <input
                              type="checkbox"
                              checked={
                                editingEmployee.institutes?.includes(
                                  institute.name
                                ) || false
                              }
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setEditingEmployee((prev) => ({
                                  ...prev,
                                  institutes: isChecked
                                    ? [
                                        ...(prev.institutes || []),
                                        institute.name,
                                      ]
                                    : (prev.institutes || []).filter(
                                        (name) => name !== institute.name
                                      ),
                                }));
                              }}
                              style={{
                                marginRight: "0.5rem",
                                accentColor: "#3b82f6",
                              }}
                            />
                            <div>
                              <div
                                style={{
                                  fontWeight: "500",
                                  fontSize: "0.75rem",
                                }}
                              >
                                {institute.name}
                              </div>
                              <div
                                style={{
                                  fontSize: "0.625rem",
                                  color: "#6b7280",
                                }}
                              >
                                {institute.code}
                              </div>
                            </div>
                          </label>
                        ))
                      ) : (
                        <div
                          style={{
                            padding: "0.5rem",
                            textAlign: "center",
                            color: "#6b7280",
                            fontSize: "0.75rem",
                          }}
                        >
                          No institutes available
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <label
                      style={{
                        display: "block",
                        fontSize: "0.875rem",
                        fontWeight: "500",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Products *
                    </label>
                    <div
                      style={{
                        border: "1px solid #d1d5db",
                        borderRadius: "0.375rem",
                        padding: "0.5rem",
                        backgroundColor: "#f9fafb",
                        maxHeight: "120px",
                        overflowY: "auto",
                      }}
                    >
                      {masters.products.length > 0 ? (
                        masters.products.map((product: any) => (
                          <label
                            key={product.id}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              padding: "0.25rem",
                              cursor: "pointer",
                              borderRadius: "0.25rem",
                              marginBottom: "0.25rem",
                              backgroundColor:
                                editingEmployee.products?.includes(product.name)
                                  ? "#dcfce7"
                                  : "transparent",
                              border: editingEmployee.products?.includes(
                                product.name
                              )
                                ? "1px solid #16a34a"
                                : "1px solid transparent",
                            }}
                          >
                            <input
                              type="checkbox"
                              checked={
                                editingEmployee.products?.includes(
                                  product.name
                                ) || false
                              }
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setEditingEmployee((prev) => ({
                                  ...prev,
                                  products: isChecked
                                    ? [...(prev.products || []), product.name]
                                    : (prev.products || []).filter(
                                        (name) => name !== product.name
                                      ),
                                }));
                              }}
                              style={{
                                marginRight: "0.5rem",
                                accentColor: "#16a34a",
                              }}
                            />
                            <div>
                              <div
                                style={{
                                  fontWeight: "500",
                                  fontSize: "0.75rem",
                                }}
                              >
                                {product.name}
                              </div>
                              <div
                                style={{
                                  fontSize: "0.625rem",
                                  color: "#6b7280",
                                }}
                              >
                                {product.code}
                              </div>
                            </div>
                          </label>
                        ))
                      ) : (
                        <div
                          style={{
                            padding: "0.5rem",
                            textAlign: "center",
                            color: "#6b7280",
                            fontSize: "0.75rem",
                          }}
                        >
                          No products available
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Active Status */}
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={editingEmployee.isActive !== false} // Default to true if undefined
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        isActive: e.target.checked,
                      })
                    }
                    style={{
                      accentColor: "#10b981",
                    }}
                  />
                  Active Employee
                </label>
                <div
                  style={{
                    fontSize: "0.75rem",
                    color: "#6b7280",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  Uncheck to deactivate this employee
                </div>
              </div>

              <div style={{ display: "flex", gap: "1rem", marginTop: "2rem" }}>
                <button
                  onClick={handleUpdateEmployee}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  ✅ Update Employee
                </button>
                <button
                  onClick={() => {
                    setShowEditEmployeeModal(false);
                    setEditingEmployee(null);
                  }}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#6b7280",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Role Modal */}
        {showEditRoleModal && editingRole && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                width: "90%",
                maxWidth: "400px",
              }}
            >
              <h3
                style={{
                  fontSize: "1.25rem",
                  fontWeight: "bold",
                  marginBottom: "1.5rem",
                }}
              >
                ✏️ Edit Role
              </h3>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Role Name *
                </label>
                <input
                  type="text"
                  value={editingRole.name}
                  onChange={(e) =>
                    setEditingRole({ ...editingRole, name: e.target.value })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={editingRole.isActive !== false} // Default to true if undefined
                    onChange={(e) =>
                      setEditingRole({
                        ...editingRole,
                        isActive: e.target.checked,
                      })
                    }
                    style={{
                      accentColor: "#3b82f6",
                    }}
                  />
                  Active Role
                </label>
                <div
                  style={{
                    fontSize: "0.75rem",
                    color: "#6b7280",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  Uncheck to deactivate this role
                </div>
              </div>

              <div style={{ display: "flex", gap: "1rem", marginTop: "2rem" }}>
                <button
                  onClick={handleUpdateRole}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  ✅ Update Role
                </button>
                <button
                  onClick={() => {
                    setShowEditRoleModal(false);
                    setEditingRole(null);
                  }}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#6b7280",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Team Modal */}
        {showEditTeamModal && editingTeam && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                padding: "2rem",
                borderRadius: "0.5rem",
                width: "90%",
                maxWidth: "400px",
              }}
            >
              <h3
                style={{
                  fontSize: "1.25rem",
                  fontWeight: "bold",
                  marginBottom: "1.5rem",
                }}
              >
                ✏️ Edit Team
              </h3>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Team Name *
                </label>
                <input
                  type="text"
                  value={editingTeam.name}
                  onChange={(e) =>
                    setEditingTeam({ ...editingTeam, name: e.target.value })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    marginBottom: "0.5rem",
                  }}
                >
                  Description
                </label>
                <input
                  type="text"
                  value={editingTeam.description || ""}
                  onChange={(e) =>
                    setEditingTeam({
                      ...editingTeam,
                      description: e.target.value,
                    })
                  }
                  style={{
                    width: "100%",
                    padding: "0.75rem",
                    border: "1px solid #d1d5db",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    boxSizing: "border-box",
                  }}
                />
              </div>

              {/* Active Status */}
              <div style={{ marginBottom: "1rem" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={editingTeam.isActive !== false} // Default to true if undefined
                    onChange={(e) =>
                      setEditingTeam({
                        ...editingTeam,
                        isActive: e.target.checked,
                      })
                    }
                    style={{
                      accentColor: "#10b981",
                    }}
                  />
                  Active Team
                </label>
                <div
                  style={{
                    fontSize: "0.75rem",
                    color: "#6b7280",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  Uncheck to deactivate this team
                </div>
              </div>

              <div style={{ display: "flex", gap: "1rem", marginTop: "2rem" }}>
                <button
                  onClick={handleUpdateTeam}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  ✅ Update Team
                </button>
                <button
                  onClick={() => {
                    setShowEditTeamModal(false);
                    setEditingTeam(null);
                  }}
                  style={{
                    flex: 1,
                    padding: "0.75rem",
                    backgroundColor: "#6b7280",
                    color: "white",
                    border: "none",
                    borderRadius: "0.375rem",
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    cursor: "pointer",
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notifications */}
        <div
          style={{
            position: "fixed",
            top: "1rem",
            right: "1rem",
            zIndex: 1000,
          }}
        >
          {notifications.map((notification) => (
            <div
              key={notification.id}
              style={{
                marginBottom: "0.5rem",
                padding: "0.75rem 1rem",
                backgroundColor:
                  notification.type === "error"
                    ? "#fef2f2"
                    : notification.type === "warning"
                    ? "#fffbeb"
                    : notification.type === "success"
                    ? "#f0fdf4"
                    : "#eff6ff",
                color:
                  notification.type === "error"
                    ? "#dc2626"
                    : notification.type === "warning"
                    ? "#d97706"
                    : notification.type === "success"
                    ? "#16a34a"
                    : "#2563eb",
                border: `1px solid ${
                  notification.type === "error"
                    ? "#fecaca"
                    : notification.type === "warning"
                    ? "#fed7aa"
                    : notification.type === "success"
                    ? "#bbf7d0"
                    : "#bfdbfe"
                }`,
                borderRadius: "0.375rem",
                fontSize: "0.875rem",
                fontWeight: "500",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                minWidth: "300px",
                maxWidth: "400px",
              }}
            >
              {notification.message}
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        padding: "1rem",
        fontFamily: "system-ui",
      }}
    >
      <div
        style={{
          width: "100%",
          maxWidth: "400px",
          backgroundColor: "white",
          padding: "2rem",
          borderRadius: "0.5rem",
          boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: "2rem" }}>
          <div style={{ fontSize: "3rem", marginBottom: "1rem" }}>🛡️</div>
          <h1 style={{ fontSize: "1.5rem", fontWeight: "bold", margin: 0 }}>
            ACS Billing System
          </h1>
          <p style={{ color: "#64748b", margin: "0.5rem 0 0 0" }}>
            Sign in to your account
          </p>
        </div>

        <form onSubmit={handleLogin}>
          {error && (
            <div
              style={{
                padding: "0.75rem",
                backgroundColor: "#fef2f2",
                border: "1px solid #fecaca",
                borderRadius: "0.375rem",
                color: "#dc2626",
                fontSize: "0.875rem",
                marginBottom: "1rem",
              }}
            >
              {error}
            </div>
          )}

          <div style={{ marginBottom: "1rem" }}>
            <label
              style={{
                display: "block",
                fontSize: "0.875rem",
                fontWeight: "500",
                marginBottom: "0.5rem",
              }}
            >
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              style={{
                width: "100%",
                padding: "0.75rem",
                border: "1px solid #d1d5db",
                borderRadius: "0.375rem",
                fontSize: "0.875rem",
                boxSizing: "border-box",
              }}
            />
          </div>

          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                fontSize: "0.875rem",
                fontWeight: "500",
                marginBottom: "0.5rem",
              }}
            >
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              style={{
                width: "100%",
                padding: "0.75rem",
                border: "1px solid #d1d5db",
                borderRadius: "0.375rem",
                fontSize: "0.875rem",
                boxSizing: "border-box",
              }}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            style={{
              width: "100%",
              padding: "0.75rem",
              backgroundColor: loading ? "#9ca3af" : "#3b82f6",
              color: "white",
              border: "none",
              borderRadius: "0.375rem",
              fontSize: "0.875rem",
              fontWeight: "500",
              cursor: loading ? "not-allowed" : "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {loading ? "Signing in..." : "🔐 Sign in"}
          </button>

          <button
            type="button"
            onClick={testConnection}
            style={{
              width: "100%",
              padding: "0.5rem",
              backgroundColor: "#10b981",
              color: "white",
              border: "none",
              borderRadius: "0.375rem",
              fontSize: "0.75rem",
              fontWeight: "500",
              cursor: "pointer",
              marginTop: "0.5rem",
            }}
          >
            🔗 Test Backend Connection
          </button>

          <div
            style={{
              marginTop: "1rem",
              padding: "0.75rem",
              backgroundColor: "#f8fafc",
              borderRadius: "0.375rem",
              fontSize: "0.75rem",
              textAlign: "center",
              color: "#64748b",
            }}
          >
            <div>
              <strong>Demo credentials:</strong>
            </div>
            <div>Super Admin: <EMAIL> / password</div>
            <div>Admin: <EMAIL> / password</div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default App;
