const express = require("express");
const { body, validationResult } = require("express-validator");
const errorRecoveryManager = require("../middleware/errorRecovery");
const { query } = require("../config/database");

const router = express.Router();

// Simplified validation rules
const instituteValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Institute name must be between 2 and 100 characters")
    .customSanitizer((value) => {
      return value.replace(/\b\w/g, (char) => char.toUpperCase());
    }),
];

const locationValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location name must be between 2 and 100 characters"),
];

const productValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Product name must be between 2 and 100 characters"),
];

const serviceValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Service name must be between 2 and 100 characters"),
];

const rateValidation = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Rate name must be between 2 and 100 characters"),
];

// ==================== INSTITUTES ====================

// GET /api/masters/institutes - Get all institutes
router.get("/institutes", async (req, res) => {
  try {
    const { search = "", isActive = "" } = req.query;

    let whereClause = "WHERE 1=1";
    let queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (LOWER(name) LIKE $${paramCount} OR LOWER(code) LIKE $${paramCount} OR LOWER(location) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (isActive !== "") {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    const result = await query(
      `SELECT id, name, code, location, contact_person, contact_email, contact_phone, address, is_active, created_at, updated_at
       FROM institutes ${whereClause} ORDER BY name`,
      queryParams
    );

    res.json({
      institutes: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get institutes error:", error);
    res.status(500).json({
      error: "Failed to retrieve institutes",
      message: "An error occurred while fetching institute data",
    });
  }
});

// POST /api/masters/institutes - Create new institute
router.post("/institutes", instituteValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      name,
      code = name.toLowerCase().replace(/\s+/g, "_"),
      isActive = true,
    } = req.body;

    // Check for duplicate name or code
    const existingInstitute = await query(
      "SELECT id FROM institutes WHERE LOWER(name) = LOWER($1) OR LOWER(code) = LOWER($2)",
      [name, code]
    );

    if (existingInstitute.rows.length > 0) {
      return res.status(409).json({
        error: "Institute already exists",
        message: `Institute with name '${name}' or code '${code}' already exists`,
      });
    }

    const result = await query(
      `INSERT INTO institutes (name, code, is_active)
       VALUES ($1, $2, $3)
       RETURNING id, name, code, location, contact_person, contact_email, contact_phone, address, is_active, created_at, updated_at`,
      [name, code, isActive]
    );

    res.status(201).json({
      message: "Institute created successfully",
      institute: result.rows[0],
    });
  } catch (error) {
    console.error("Create institute error:", error);
    res.status(500).json({
      error: "Failed to create institute",
      message: "An error occurred while creating the institute",
    });
  }
});

// PUT /api/masters/institutes/:id - Update institute
router.put("/institutes/:id", instituteValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    // Check if institute exists
    const existingInstitute = await query(
      "SELECT id FROM institutes WHERE id = $1",
      [id]
    );

    if (existingInstitute.rows.length === 0) {
      return res.status(404).json({
        error: "Institute not found",
        message: `Institute with ID ${id} does not exist`,
      });
    }

    // Update the institute
    const result = await query(
      `UPDATE institutes SET name = $1, is_active = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING id, name, code, location, contact_person, contact_email, contact_phone, address, is_active, created_at, updated_at`,
      [name, isActive, id]
    );

    res.json({
      message: "Institute updated successfully",
      institute: result.rows[0],
    });
  } catch (error) {
    console.error("Update institute error:", error);
    res.status(500).json({
      error: "Failed to update institute",
      message: "An error occurred while updating the institute",
    });
  }
});

// DELETE /api/masters/institutes/:id - Delete institute
router.delete("/institutes/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      "DELETE FROM institutes WHERE id = $1 RETURNING id",
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Institute not found",
        message: `Institute with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Institute deleted successfully",
    });
  } catch (error) {
    console.error("Delete institute error:", error);
    res.status(500).json({
      error: "Failed to delete institute",
      message: "An error occurred while deleting the institute",
    });
  }
});

// ==================== LOCATIONS ====================

// GET /api/masters/locations - Get all locations
router.get("/locations", async (req, res) => {
  try {
    const { search = "", type = "", isActive = "" } = req.query;

    let whereClause = "WHERE 1=1";
    let queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (LOWER(name) LIKE $${paramCount} OR LOWER(code) LIKE $${paramCount} OR LOWER(description) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (type) {
      paramCount++;
      whereClause += ` AND type = $${paramCount}`;
      queryParams.push(type);
    }

    if (isActive !== "") {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    const result = await query(
      `SELECT id, name, code, type, description, is_active, created_at, updated_at
       FROM locations ${whereClause} ORDER BY name`,
      queryParams
    );

    res.json({
      locations: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get locations error:", error);
    res.status(500).json({
      error: "Failed to retrieve locations",
      message: "An error occurred while fetching location data",
    });
  }
});

// POST /api/masters/locations - Create new location
router.post("/locations", locationValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      name,
      code = name.toLowerCase().replace(/\s+/g, "_"),
      type = "local",
      isActive = true,
    } = req.body;

    // Check for duplicate name or code
    const existingLocation = await query(
      "SELECT id FROM locations WHERE LOWER(name) = LOWER($1) OR LOWER(code) = LOWER($2)",
      [name, code]
    );

    if (existingLocation.rows.length > 0) {
      return res.status(409).json({
        error: "Location already exists",
        message: `Location with name '${name}' or code '${code}' already exists`,
      });
    }

    const result = await query(
      `INSERT INTO locations (name, code, type, is_active)
       VALUES ($1, $2, $3, $4)
       RETURNING id, name, code, type, description, is_active, created_at, updated_at`,
      [name, code, type, isActive]
    );

    res.status(201).json({
      message: "Location created successfully",
      location: result.rows[0],
    });
  } catch (error) {
    console.error("Create location error:", error);
    res.status(500).json({
      error: "Failed to create location",
      message: "An error occurred while creating the location",
    });
  }
});

// PUT /api/masters/locations/:id - Update location
router.put("/locations/:id", locationValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    const result = await query(
      `UPDATE locations SET name = $1, is_active = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING id, name, code, type, description, is_active, created_at, updated_at`,
      [name, isActive, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Location not found",
        message: `Location with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Location updated successfully",
      location: result.rows[0],
    });
  } catch (error) {
    console.error("Update location error:", error);
    res.status(500).json({
      error: "Failed to update location",
      message: "An error occurred while updating the location",
    });
  }
});

// DELETE /api/masters/locations/:id - Delete location
router.delete("/locations/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      "DELETE FROM locations WHERE id = $1 RETURNING id",
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Location not found",
        message: `Location with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Location deleted successfully",
    });
  } catch (error) {
    console.error("Delete location error:", error);
    res.status(500).json({
      error: "Failed to delete location",
      message: "An error occurred while deleting the location",
    });
  }
});

// ==================== PRODUCTS ====================

// GET /api/masters/products - Get all products
router.get("/products", async (req, res) => {
  try {
    const { search = "", category = "", isActive = "" } = req.query;

    let whereClause = "WHERE 1=1";
    let queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (LOWER(name) LIKE $${paramCount} OR LOWER(code) LIKE $${paramCount} OR LOWER(description) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (category) {
      paramCount++;
      whereClause += ` AND category = $${paramCount}`;
      queryParams.push(category);
    }

    if (isActive !== "") {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    const result = await query(
      `SELECT id, name, code, category, description, is_active, created_at, updated_at
       FROM products ${whereClause} ORDER BY name`,
      queryParams
    );

    res.json({
      products: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get products error:", error);
    res.status(500).json({
      error: "Failed to retrieve products",
      message: "An error occurred while fetching product data",
    });
  }
});

// POST /api/masters/products - Create new product
router.post("/products", productValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      name,
      code = name.toLowerCase().replace(/\s+/g, "_"),
      isActive = true,
    } = req.body;

    // Check for duplicate name or code
    const existingProduct = await query(
      "SELECT id FROM products WHERE LOWER(name) = LOWER($1) OR LOWER(code) = LOWER($2)",
      [name, code]
    );

    if (existingProduct.rows.length > 0) {
      return res.status(409).json({
        error: "Product already exists",
        message: `Product with name '${name}' or code '${code}' already exists`,
      });
    }

    const result = await query(
      `INSERT INTO products (name, code, category, is_active)
       VALUES ($1, $2, 'General', $3)
       RETURNING id, name, code, category, description, is_active, created_at, updated_at`,
      [name, code, isActive]
    );

    res.status(201).json({
      message: "Product created successfully",
      product: result.rows[0],
    });
  } catch (error) {
    console.error("Create product error:", error);
    res.status(500).json({
      error: "Failed to create product",
      message: "An error occurred while creating the product",
    });
  }
});

// PUT /api/masters/products/:id - Update product
router.put("/products/:id", productValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    const result = await query(
      `UPDATE products SET name = $1, is_active = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING id, name, code, category, description, is_active, created_at, updated_at`,
      [name, isActive, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Product not found",
        message: `Product with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Product updated successfully",
      product: result.rows[0],
    });
  } catch (error) {
    console.error("Update product error:", error);
    res.status(500).json({
      error: "Failed to update product",
      message: "An error occurred while updating the product",
    });
  }
});

// DELETE /api/masters/products/:id - Delete product
router.delete("/products/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      "DELETE FROM products WHERE id = $1 RETURNING id",
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Product not found",
        message: `Product with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Delete product error:", error);
    res.status(500).json({
      error: "Failed to delete product",
      message: "An error occurred while deleting the product",
    });
  }
});

// ==================== SERVICES ====================

// GET /api/masters/services - Get all services
router.get("/services", async (req, res) => {
  try {
    const { search = "", category = "", isActive = "" } = req.query;

    let whereClause = "WHERE 1=1";
    let queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (LOWER(name) LIKE $${paramCount} OR LOWER(code) LIKE $${paramCount} OR LOWER(description) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (category) {
      paramCount++;
      whereClause += ` AND category = $${paramCount}`;
      queryParams.push(category);
    }

    if (isActive !== "") {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    const result = await query(
      `SELECT id, name, code, category, description, is_active, created_at, updated_at
       FROM services ${whereClause} ORDER BY name`,
      queryParams
    );

    res.json({
      services: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get services error:", error);
    res.status(500).json({
      error: "Failed to retrieve services",
      message: "An error occurred while fetching service data",
    });
  }
});

// POST /api/masters/services - Create new service
router.post("/services", serviceValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      name,
      code = name.toLowerCase().replace(/\s+/g, "_"),
      isActive = true,
    } = req.body;

    // Check for duplicate name or code
    const existingService = await query(
      "SELECT id FROM services WHERE LOWER(name) = LOWER($1) OR LOWER(code) = LOWER($2)",
      [name, code]
    );

    if (existingService.rows.length > 0) {
      return res.status(409).json({
        error: "Service already exists",
        message: `Service with name '${name}' or code '${code}' already exists`,
      });
    }

    const result = await query(
      `INSERT INTO services (name, code, category, is_active)
       VALUES ($1, $2, 'General', $3)
       RETURNING id, name, code, category, description, is_active, created_at, updated_at`,
      [name, code, isActive]
    );

    res.status(201).json({
      message: "Service created successfully",
      service: result.rows[0],
    });
  } catch (error) {
    console.error("Create service error:", error);
    res.status(500).json({
      error: "Failed to create service",
      message: "An error occurred while creating the service",
    });
  }
});

// PUT /api/masters/services/:id - Update service
router.put("/services/:id", serviceValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const { id } = req.params;
    const { name, isActive = true } = req.body;

    const result = await query(
      `UPDATE services SET name = $1, is_active = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING id, name, code, category, description, is_active, created_at, updated_at`,
      [name, isActive, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Service not found",
        message: `Service with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Service updated successfully",
      service: result.rows[0],
    });
  } catch (error) {
    console.error("Update service error:", error);
    res.status(500).json({
      error: "Failed to update service",
      message: "An error occurred while updating the service",
    });
  }
});

// DELETE /api/masters/services/:id - Delete service
router.delete("/services/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      "DELETE FROM services WHERE id = $1 RETURNING id",
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: "Service not found",
        message: `Service with ID ${id} does not exist`,
      });
    }

    res.json({
      message: "Service deleted successfully",
    });
  } catch (error) {
    console.error("Delete service error:", error);
    res.status(500).json({
      error: "Failed to delete service",
      message: "An error occurred while deleting the service",
    });
  }
});

// ==================== RATES ====================

// GET /api/masters/rates - Get all rates
router.get("/rates", async (req, res) => {
  try {
    const { search = "", role = "", isActive = "" } = req.query;

    let whereClause = "WHERE 1=1";
    let queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (LOWER(role) LIKE $${paramCount} OR LOWER(level) LIKE $${paramCount} OR LOWER(location_type) LIKE $${paramCount})`;
      queryParams.push(`%${search.toLowerCase()}%`);
    }

    if (role) {
      paramCount++;
      whereClause += ` AND role = $${paramCount}`;
      queryParams.push(role);
    }

    if (isActive !== "") {
      paramCount++;
      whereClause += ` AND is_active = $${paramCount}`;
      queryParams.push(isActive === "true");
    }

    const result = await query(
      `SELECT id, role, level, location_type, daily_rate, hourly_rate, effective_from, effective_to, is_active, created_at, updated_at
       FROM rates ${whereClause} ORDER BY role, level`,
      queryParams
    );

    res.json({
      rates: result.rows,
      total: result.rows.length,
    });
  } catch (error) {
    console.error("Get rates error:", error);
    res.status(500).json({
      error: "Failed to retrieve rates",
      message: "An error occurred while fetching rate data",
    });
  }
});

// POST /api/masters/rates - Create new rate
router.post("/rates", rateValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: "Validation failed",
        details: errors.array(),
      });
    }

    const {
      role = "general",
      level = "standard",
      locationType = "local",
      dailyRate = 0,
      hourlyRate = 0,
      effectiveFrom = new Date().toISOString().split("T")[0],
      effectiveTo = null,
      isActive = true,
    } = req.body;

    const result = await query(
      `INSERT INTO rates (role, level, location_type, daily_rate, hourly_rate, effective_from, effective_to, is_active)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING id, role, level, location_type, daily_rate, hourly_rate, effective_from, effective_to, is_active, created_at, updated_at`,
      [
        role,
        level,
        locationType,
        dailyRate,
        hourlyRate,
        effectiveFrom,
        effectiveTo,
        isActive,
      ]
    );

    res.status(201).json({
      message: "Rate created successfully",
      rate: result.rows[0],
    });
  } catch (error) {
    console.error("Create rate error:", error);
    res.status(500).json({
      error: "Failed to create rate",
      message: "An error occurred while creating the rate",
    });
  }
});

module.exports = router;
